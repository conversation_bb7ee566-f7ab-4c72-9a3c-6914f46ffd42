import request from '@/utils/request'

// 查询指标分类列表
export function listCategory(query) {
  return request({
    url: '/screen/category/list',
    method: 'get',
    params: query
  })
}
// 查询指标分类列表
export function listTreeCategory(query) {
  return request({
    url: '/screen/category/treelist',
    method: 'get',
    params: query
  })
}

// 查询指标分类详细
export function getCategory(id) {
  return request({
    url: '/screen/category/' + id,
    method: 'get'
  })
}

// 新增指标分类
export function addCategory(data) {
  return request({
    url: '/screen/category',
    method: 'post',
    data: data
  })
}

// 修改指标分类
export function updateCategory(data) {
  return request({
    url: '/screen/category',
    method: 'put',
    data: data
  })
}

// 删除指标分类
export function delCategory(id) {
  return request({
    url: '/screen/category/' + id,
    method: 'delete'
  })
}

// 导出指标分类
export function exportCategory(query) {
  return request({
    url: '/screen/category/export',
    method: 'get',
    params: query
  })
}
// 查询指标分类详细
export function getCategoryCounty() {
  return request({
    url: '/screen/category/counties',
    method: 'get'
  })
}

// 查询指标分类详细
export function admLevel() {
  return request({
    url: '/screen/category/admLevel',
    method: 'get'
  })
}
