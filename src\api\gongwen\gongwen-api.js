import axios from "axios";
import { GONGWEN_CONFIG } from "@/config/gongwen";

// API 配置 - 从配置文件读取
const API_CONFIG = GONGWEN_CONFIG;

// 创建专用的 axios 实例，用于公文生成 API
const gongwenService = axios.create({
  timeout: API_CONFIG.timeout,
});

// 添加请求拦截器
gongwenService.interceptors.request.use(
  (config) => {
    console.log("发送公文生成请求:", config);
    return config;
  },
  (error) => {
    console.error("请求拦截器错误:", error);
    return Promise.reject(error);
  }
);

// 添加响应拦截器
gongwenService.interceptors.response.use(
  (response) => {
    console.log("公文生成响应:", response);
    return response;
  },
  (error) => {
    console.error("响应拦截器错误:", error);
    return Promise.reject(error);
  }
);

/**
 * 公文生成接口（阻塞模式）
 * @param {Object} params - 输入参数
 * @param {string} params.type - 公文类型：通告/通知/批复
 * @param {string} params.title - 公文标题
 * @param {string} params.requirements - 内容要求
 * @param {string[]|string} params.outline - 大纲（可以是数组或字符串）
 * @param {number} params.min - 最小字数
 * @param {number} params.max - 最大字数
 * @returns {Promise<Object>} 生成结果
 */
export async function generateGongwen(params) {
  try {
    // 验证必需参数
    const requiredFields = [
      "type",
      "title",
      "requirements",
      "outline",
      "min",
      "max",
    ];
    for (const field of requiredFields) {
      if (!params[field]) {
        throw new Error(`缺少必需参数: ${field}`);
      }
    }

    // 处理 outline 参数（如果是数组则拼接为字符串）
    let outlineValue = params.outline;
    if (Array.isArray(outlineValue)) {
      outlineValue = outlineValue.join("\n");
    }

    // 组装请求体
    const requestData = {
      inputs: {
        type: params.type,
        title: params.title,
        outline: outlineValue,
        requirements: params.requirements,
        min: params.min,
        max: params.max,
      },
      response_mode: "blocking",
      user: "api",
    };

    console.log("发送请求:", JSON.stringify(requestData, null, 2));

    // 发送请求
    const response = await gongwenService.post(API_CONFIG.url, requestData, {
      headers: {
        Authorization: `Bearer ${API_CONFIG.apiKey}`,
        "Content-Type": "application/json",
      },
    });

    console.log("HTTP 状态码:", response.status);
    console.log("原始响应:", response.data);

    // 解析响应结果
    const result = response.data;
    const outputs = result?.data?.outputs || {};
    const generatedDoc = outputs.result || "未生成内容";

    return {
      success: true,
      data: {
        content: generatedDoc,
        outputs: outputs,
        rawResponse: result,
      },
    };
  } catch (error) {
    console.error("公文生成失败:", error.message);

    // 如果是 axios 错误，提取更多信息
    if (error.response) {
      console.error("响应状态码:", error.response.status);
      console.error("响应数据:", error.response.data);

      return {
        success: false,
        error: {
          message: error.message,
          status: error.response.status,
          data: error.response.data,
        },
      };
    }

    return {
      success: false,
      error: {
        message: error.message,
      },
    };
  }
}

/**
 * 公文生成接口（流式模式）
 * @param {Object} params - 输入参数
 * @param {string} params.type - 公文类型：通告/通知/批复
 * @param {string} params.title - 公文标题
 * @param {string} params.requirements - 内容要求
 * @param {string[]|string} params.outline - 大纲（可以是数组或字符串）
 * @param {number} params.min - 最小字数
 * @param {number} params.max - 最大字数
 * @param {Function} onProgress - 流式数据回调函数 (chunk) => void
 * @param {Function} onComplete - 完成回调函数 (finalContent) => void
 * @param {Function} onError - 错误回调函数 (error) => void
 * @returns {Promise<Object>} 生成结果
 */
export async function generateGongwenStream(
  params,
  onProgress,
  onComplete,
  onError
) {
  try {
    // 验证必需参数
    const requiredFields = [
      "type",
      "title",
      "requirements",
      "outline",
      "min",
      "max",
    ];
    for (const field of requiredFields) {
      if (!params[field]) {
        throw new Error(`缺少必需参数: ${field}`);
      }
    }

    // 处理 outline 参数（如果是数组则拼接为字符串）
    let outlineValue = params.outline;
    if (Array.isArray(outlineValue)) {
      outlineValue = outlineValue.join("\n");
    }

    // 组装请求体（使用流式模式）
    const requestData = {
      inputs: {
        type: params.type,
        title: params.title,
        outline: outlineValue,
        requirements: params.requirements,
        min: params.min,
        max: params.max,
      },
      response_mode: "streaming",
      user: "api",
    };

    console.log("发送流式请求:", JSON.stringify(requestData, null, 2));

    // 使用 fetch 进行流式请求
    const response = await fetch(API_CONFIG.url, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${API_CONFIG.apiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = "";
    let fullContent = "";

    try {
      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          break;
        }

        // 解码数据块
        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;

        // 处理完整的行
        const lines = buffer.split("\n");
        buffer = lines.pop() || ""; // 保留不完整的行

        for (const line of lines) {
          if (line.trim() === "") continue;

          try {
            // 解析 SSE 格式的数据
            if (line.startsWith("data: ")) {
              const jsonStr = line.slice(6); // 移除 "data: " 前缀

              if (jsonStr === "[DONE]") {
                // 流式传输完成
                if (onComplete) {
                  onComplete(fullContent);
                }
                break;
              }

              const data = JSON.parse(jsonStr);

              // 调试：打印接收到的数据结构
              console.log("流式数据事件:", data.event, data);

              // 处理流式文本块
              if (data.event === "text_chunk" && data.data && data.data.text) {
                const deltaContent = data.data.text;
                fullContent += deltaContent;

                // 调用进度回调
                if (onProgress) {
                  onProgress(deltaContent, fullContent);
                }
              }
              // 处理工作流完成事件
              else if (
                data.event === "workflow_finished" &&
                data.data &&
                data.data.outputs &&
                data.data.outputs.result
              ) {
                // 获取完整的结果内容
                const finalResult = data.data.outputs.result;

                // 如果之前没有接收到内容，使用最终结果
                if (!fullContent || fullContent.trim() === "") {
                  fullContent = finalResult;

                  // 调用进度回调显示完整内容
                  if (onProgress) {
                    onProgress(finalResult, fullContent);
                  }
                }

                console.log("工作流完成，最终结果:", finalResult);

                // 调用完成回调
                if (onComplete) {
                  onComplete(fullContent);
                }
                break; // 结束流式处理
              }
              // 处理其他可能的消息事件（兼容性）
              else if (data.event === "agent_message" && data.answer) {
                const deltaContent = data.answer;
                fullContent += deltaContent;

                // 调用进度回调
                if (onProgress) {
                  onProgress(deltaContent, fullContent);
                }
              }
              // 处理思考过程事件（兼容性）
              else if (data.event === "agent_thought" && data.thought) {
                const thoughtContent = `[思考] ${data.thought}\n\n`;
                fullContent += thoughtContent;

                // 调用进度回调显示思考过程
                if (onProgress) {
                  onProgress(thoughtContent, fullContent);
                }
              }
            }
          } catch (parseError) {
            console.warn("解析流式数据失败:", parseError, "Line:", line);
          }
        }
      }

      // 如果流式处理正常结束但没有收到 workflow_finished 事件，调用完成回调
      if (onComplete && fullContent) {
        onComplete(fullContent);
      }

      return {
        success: true,
        data: {
          content: fullContent,
          isStreaming: true,
        },
      };
    } finally {
      reader.releaseLock();
    }
  } catch (error) {
    console.error("流式公文生成失败:", error.message);

    if (onError) {
      onError(error);
    }

    return {
      success: false,
      error: {
        message: error.message,
      },
    };
  }
}

/**
 * 使用默认参数生成公文（与原 Python 脚本相同的示例）
 * @returns {Promise<Object>} 生成结果
 */
export async function generateDefaultGongwen() {
  const defaultParams = {
    type: "通知",
    title: "关于开展2024年度安陆市环境卫生工作检查的通知",
    requirements:
      "内容需明确检查时间、范围、标准及工作要求，语言简洁、指令清晰，符合党政机关通知格式规范",
    outline: [
      "一、检查时间安排",
      "二、检查范围与对象",
      "三、检查重点内容",
      "四、工作要求",
    ],
    min: 500,
    max: 1000,
  };

  return generateGongwen(defaultParams);
}

// 默认导出主要函数
export default {
  generateGongwen,
  generateGongwenStream,
  API_CONFIG,
};
