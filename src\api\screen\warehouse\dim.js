import request from '@/utils/request'

// 下载模板
export function downloadMb (type) {
  return request({
    url: `/system/dimTableManage/download/${type}`,
    method: 'get',
  })
}


// 获取数据来源表表名
export function getSjlybbm (params) {
  return request({
    url: '/system/tableOdps/list',
    method: 'get',
    params
  })
}

// 新增dim数据资源管理
export function addDim (data) {
  return request({
    url: '/system/dimTableManage',
    method: 'post',
    data
  })
}

// 查询dim数据资源管理列表
export function getDimList (params) {
  return request({
    url: '/system/dimTableManage/list',
    method: 'get',
    params
  })
}

// 获取dim数据资源管理详细信息
export function getDimDetail (id) {
  return request({
    url: `/system/dimTableManage/${id}`,
    method: 'get',
  })
}

// 新增dim数据资源管理
export function updateDim (data) {
  return request({
    url: '/system/dimTableManage',
    method: 'put',
    data
  })
}

// 单个删除dim数据资源管理
export function delDim (id) {
  return request({
    url: `/system/dimTableManage/${id}`,
    method: 'delete',
  })
}

// 批量删除dim数据资源管理
export function delDimBatch (ids) {
  return request({
    url: `/system/dimTableManage/batch/${ids}`,
    method: 'delete',
  })
}

// 批量修改dim表
export function batchUpdate (data) {
  return request({
    url: '/system/dimTableManage/batchUpdate',
    method: 'post',
    data
  })
}

// 查询清洗管理
export function getCleanDetail (id) {
  return request({
    url: `/system/dimTableManage/clean/${id}`,
    method: 'get',
  })
}

// 清洗管理新增
export function addClean (data) {
  return request({
    url: '/system/dimTableManage/clean',
    method: 'post',
    data
  })
}


