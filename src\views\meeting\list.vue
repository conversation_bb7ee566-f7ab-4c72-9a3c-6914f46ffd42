<template>
  <div class="meeting-minutes-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">会议纪要</h2>
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入文档标题搜索"
            prefix-icon="el-icon-search"
            clearable
            @input="handleSearch"
            class="search-input"
          />
        </div>
      </div>
      <!-- <div class="header-right">
        <el-button type="primary" icon="el-icon-plus" @click="createNewMinutes">
          新建邮件
        </el-button>
      </div> -->
    </div>

    <!-- 会议纪要列表 -->
    <div class="minutes-grid" v-loading="loading">
      <div
        v-for="(item, index) in displayList"
        :key="item.id || index"
        class="minutes-card"
        @click="openMinutes(item)"
      >
        <div class="card-header">
          <div class="card-title">{{ item.title }}</div>
          <div class="card-actions">
            <!-- <el-dropdown @command="handleCommand" trigger="click">
              <span class="el-dropdown-link">
                <i class="el-icon-more"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="{ action: 'edit', item }">
                  <i class="el-icon-edit"></i> 编辑
                </el-dropdown-item>
                <el-dropdown-item :command="{ action: 'delete', item }">
                  <i class="el-icon-delete"></i> 删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown> -->
          </div>
        </div>
        <div class="card-content">
          <div class="content-preview">
            {{ item.summary || "暂无内容摘要" }}
          </div>
        </div>
        <div class="card-footer">
          <div class="footer-left">
            <span class="date">{{ item.createTime }}</span>
          </div>
          <div class="footer-right">
            <span class="status-tag" :class="getStatusClass(item.status)">{{
              item.statusText
            }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="currentPage"
      :limit.sync="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      @pagination="handlePagination"
    />
  </div>
</template>

<script>
import {
  getMeetingList,
  deleteMeeting,
  getMeetingStatusText,
  MEETING_STATUS,
} from "@/api/meeting";
import Pagination from "@/components/Pagination";

export default {
  name: "MeetingMinutesList",
  components: {
    Pagination,
  },
  data() {
    return {
      loading: false,
      searchKeyword: "",
      currentPage: 1,
      pageSize: 12, // 一行2个，每页显示6行
      total: 0,
      minutesList: [],
      searchTimer: null, // 搜索防抖定时器
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 12,
        searchValue: "",
        title: "",
        creator: "",
        status: null,
        startTime: "",
        endTime: "",
      },
    };
  },
  computed: {
    displayList() {
      return this.minutesList.map((item) => ({
        ...item,
        // 格式化显示时间
        createTime: item.cTime,
        // 转换状态显示
        statusText: item.status === 1 ? "已结束" : "已开始",
        // 生成摘要（从纪要内容截取）
        summary: item.jy
          ? item.jy.replace(/<[^>]*>/g, "").substring(0, 100) + "..."
          : "暂无内容摘要",
      }));
    },
  },
  mounted() {
    this.loadMeetingMinutes();
  },
  methods: {
    // 加载会议纪要列表
    async loadMeetingMinutes() {
      this.loading = true;
      try {
        // 更新查询参数
        this.queryParams.pageNum = this.currentPage;
        this.queryParams.pageSize = this.pageSize;
        this.queryParams.searchValue = this.searchKeyword;

        const response = await getMeetingList(this.queryParams);

        if (response.code === 200) {
          this.minutesList = response.rows || [];
          this.total = response.total || 0;
        } else {
          this.$message.error(response.msg || "加载列表失败");
        }
      } catch (error) {
        console.error("加载会议纪要列表失败:", error);
        this.$message.error("加载列表失败");
      } finally {
        this.loading = false;
      }
    },

    // 搜索处理
    handleSearch() {
      this.currentPage = 1;
      // 延迟搜索，避免频繁请求
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.loadMeetingMinutes();
      }, 500);
    },

    // 分页处理
    handlePagination({ page, limit }) {
      this.currentPage = page;
      this.pageSize = limit;
      this.loadMeetingMinutes();
    },

    // 打开会议纪要详情
    openMinutes(item) {
      // 跳转到会议纪要编辑页面
      this.$router.push({
        path: "/meeting/editor",
        query: {
          id: item.id,
          meetingId: item.meetingId,
          title: item.title,
        },
      });
    },

    // 下拉菜单命令处理
    handleCommand(command) {
      const { action, item } = command;
      switch (action) {
        case "edit":
          this.editMinutes(item);
          break;
        case "delete":
          this.deleteMinutes(item);
          break;
      }
    },

    // 编辑会议纪要
    editMinutes(item) {
      this.$router.push({
        path: "/meeting/index",
        query: { id: item.id },
      });
    },

    // 删除会议纪要
    async deleteMinutes(item) {
      this.$confirm("确定要删除这个会议纪要吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          try {
            const response = await deleteMeeting([item.id]);
            if (response.code === 200) {
              this.$message.success("删除成功");
              this.loadMeetingMinutes();
            } else {
              this.$message.error(response.msg || "删除失败");
            }
          } catch (error) {
            console.error("删除会议纪要失败:", error);
            this.$message.error("删除失败");
          }
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },

    // 获取状态文本
    getStatusText(status) {
      return getMeetingStatusText(status);
    },

    // 获取状态样式类
    getStatusClass(status) {
      switch (status) {
        case MEETING_STATUS.STARTED:
          return "processing";
        case MEETING_STATUS.ENDED:
          return "completed";
        default:
          return "draft";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.meeting-minutes-list {
  padding: 20px;
  background-color: #fff;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    background: #fff;
    padding: 16px 20px;

    .header-left {
      display: flex;
      align-items: center;
      gap: 20px;

      .page-title {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #303133;
      }

      .search-box {
        .search-input {
          width: 300px;
        }
      }
    }

    .header-right {
      .el-button {
        font-size: 14px;
      }
    }
  }

  .my-documents-section {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;

    .section-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;

      i {
        font-size: 18px;
        color: #409eff;
      }

      span {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    .section-description {
      color: #606266;
      font-size: 14px;
      line-height: 1.6;
      margin-bottom: 16px;
    }

    .quick-start {
      .quick-start-text {
        color: #409eff;
        font-size: 14px;
        cursor: pointer;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }

  .minutes-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 30px;

    .minutes-card {
      background: #fff;
      padding: 20px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;

        .card-title {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          line-height: 1.4;
          flex: 1;
          margin-right: 10px;
        }

        .card-actions {
          .el-dropdown-link {
            color: #909399;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.3s ease;

            &:hover {
              background-color: #f5f7fa;
              color: #409eff;
            }

            i {
              font-size: 16px;
            }
          }
        }
      }

      .card-content {
        margin-bottom: 16px;

        .content-preview {
          color: #606266;
          font-size: 14px;
          line-height: 1.6;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .card-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 12px;
        border-top: 1px solid #f0f2f5;

        .footer-left {
          .date {
            color: #909399;
            font-size: 12px;
          }
        }

        .footer-right {
          .status-tag {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;

            &.draft {
              background-color: #f4f4f5;
              color: #909399;
            }

            &.processing {
              background-color: #fdf6ec;
              color: #e6a23c;
            }

            &.completed {
              background-color: #f0f9ff;
              color: #409eff;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .meeting-minutes-list {
    .minutes-grid {
      grid-template-columns: 1fr;
    }
  }
}

@media (max-width: 768px) {
  .meeting-minutes-list {
    padding: 10px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .header-left {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;

        .search-box .search-input {
          width: 100%;
        }
      }
    }

    .minutes-card {
      padding: 16px;
    }
  }
}
</style>
