import { login, logout, getInfo } from "@/api/login";
import { validateST } from "@/api/sso";
import { getToken, setToken, removeToken } from "@/utils/auth";
import { cleanTicketFromUrl } from "@/utils/sso";

const user = {
  state: {
    token: getToken(),
    name: "",
    avatar: "",
    roles: [],
    permissions: [],
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token;
    },
    SET_NAME: (state, name) => {
      state.name = name;
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar;
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles;
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions;
    },
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      const username = userInfo?.username?.trim();
      const password = userInfo?.password;
      const code = userInfo?.code;
      const uuid = userInfo?.uuid;
      const zzdCode = userInfo?.zzdCode;
      return new Promise((resolve, reject) => {
        login(username, password, code, uuid, zzdCode)
          .then((res) => {
            setToken(res.token);
            commit("SET_TOKEN", res.token);
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo(state.token)
          .then((res) => {
            const user = res.user;
            const avatar =
              user.avatar == ""
                ? require("@/assets/images/profile.jpg")
                : process.env.VUE_APP_BASE_API + user.avatar;
            if (res.roles && res.roles.length > 0) {
              // 验证返回的roles是否是一个非空数组
              commit("SET_ROLES", res.roles);
              commit("SET_PERMISSIONS", res.permissions);
            } else {
              commit("SET_ROLES", ["ROLE_DEFAULT"]);
            }
            commit("SET_NAME", user.userName);
            commit("SET_AVATAR", avatar);
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token)
          .then(() => {
            commit("SET_TOKEN", "");
            commit("SET_ROLES", []);
            commit("SET_PERMISSIONS", []);
            removeToken();
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise((resolve) => {
        commit("SET_TOKEN", "");
        removeToken();
        resolve();
      });
    },

    // 单点登录ST验证
    SSOLogin({ commit }, { ticket, service }) {
      return new Promise((resolve, reject) => {
        validateST(ticket, service)
          .then((res) => {
            // 检查返回结果
            if (res.code === 200 && res.flag && res.token) {
              setToken(res.token);
              commit("SET_TOKEN", res.token);

              // 清除URL中的ticket参数
              cleanTicketFromUrl();

              resolve({
                token: res.token,
                username: res.username,
                msg: res.msg,
              });
            } else {
              reject(new Error(res.msg || "ST验证失败：未获取到token"));
            }
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
  },
};

export default user;
