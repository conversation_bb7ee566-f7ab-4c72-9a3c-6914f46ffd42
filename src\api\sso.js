import request from "@/utils/request";

/**
 * 单点登录ST验证
 * @param {string} ticket - ST票据
 * @param {string} service - 服务地址
 */
export function validateST(ticket, service) {
  return request({
    url: "/azyLogin",
    method: "post",
    data: {
      ticket,
      serviceUrl: service,
    },
  });
}

/**
 * 单点登录退出
 */
export function ssoLogout() {
  return request({
    url: "/sso/logout",
    method: "post",
  });
}
