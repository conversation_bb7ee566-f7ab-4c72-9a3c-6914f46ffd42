<template>
  <div class="meeting-editor-page">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧富文本编辑器 -->
      <div class="left-editor">
        <div class="editor-header">
          <div class="header-left">
            <el-button
              size="small"
              icon="el-icon-arrow-left"
              @click="goBack"
              class="back-button"
            >
              返回
            </el-button>
            <h3>{{ meetingInfo.title || '会议纪要编辑' }}</h3>
          </div>
          <div class="editor-actions">
            <el-button size="small" @click="saveDocument">
              <i class="el-icon-document"></i>
              保存
            </el-button>
          </div>
        </div>
        <div class="editor-container">
          <TinyEditor
            v-model="documentContent"
            :height="editorHeight"
            :showExport="true"
            :exportFilename="`${meetingInfo.title || '会议纪要'}.docx`"
            :preserveRawHtml="true"
            :autoScroll="true"
            export-method="jquery-plugin"
            @on-change="handleContentChange"
            @on-export-word="handleExportWord"
          />
        </div>
      </div>

      <!-- 右侧会议信息面板 -->
      <div class="right-panel">
        <div class="panel-header">
          <h3>会议信息</h3>
        </div>

        <!-- Tab切换 -->
        <el-tabs v-model="activeTab" class="meeting-tabs">
          <!-- AI小助手 Tab -->
          <el-tab-pane label="AI小助手" name="ai">
            <div class="ai-assistant-panel">
              <!-- AI对话区域 -->
              <div class="chat-container" ref="chatContainer">
                <div class="chat-messages">
                  <div
                    v-for="(message, index) in chatMessages"
                    :key="index"
                    class="message-item"
                    :class="message.type"
                  >
                    <!-- AI消息 -->
                    <div v-if="message.type === 'ai'" class="message-content">
                      <div class="ai-avatar">
                        <i class="el-icon-cpu"></i>
                      </div>
                      <div class="message-text">
                        <!-- 思考过程 -->
                        <div v-if="message.contentType === 'stream'" class="stream-block">
                          <h4>
                            <i :class="message.content.thinkingCompleted ? 'el-icon-check' : 'el-icon-loading'"></i>
                            思考过程
                            <span v-if="!message.content.thinking" style="color: #909399; font-size: 12px;">(等待中...)</span>
                          </h4>
                          <div class="thinking-text">
                            {{ message.content.thinking || '正在思考中...' }}
                          </div>
                        </div>
                        <!-- AI回答 -->
                        <div class="ai-response">
                          {{ message.contentType === 'stream' ? message.content.answer : message.content }}
                        </div>
                        <div class="message-time">{{ message.time }}</div>
                      </div>
                    </div>

                    <!-- 用户消息 -->
                    <div v-else class="message-content user-message">
                      <div class="message-text user-text">
                        {{ message.content }}
                        <div class="message-time">{{ message.time }}</div>
                      </div>
                      <div class="user-avatar">
                        <i class="el-icon-user"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>



              <!-- AI快捷操作区域 -->
              <div class="ai-quick-actions">
                <el-button
                  v-if="generatingMinutes"
                  size="small"
                  @click="stopGeneration"
                  icon="el-icon-video-pause"
                >
                  停止生成
                </el-button>

                <el-button
                  size="small"
                  @click="clearAIChat"
                  icon="el-icon-delete"
                  type="text"
                >
                  清空对话
                </el-button>
              </div>

              <!-- AI输入区域 -->
              <div class="ai-input-container">
                <el-input
                  v-model="aiInputMessage"
                  type="textarea"
                  :rows="2"
                  placeholder="请输入您的问题或需求..."
                  @keyup.ctrl.enter.native="sendAIMessage"
                  resize="none"
                />
                <div class="input-actions">
                  <span class="send-tip">Ctrl+Enter 发送</span>
                  <el-button
                    type="primary"
                    size="small"
                    @click="sendAIMessage"
                    :disabled="!aiInputMessage.trim()"
                    :loading="aiSending"
                  >
                    发送
                  </el-button>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 会议原文 Tab -->
          <el-tab-pane label="会议原文" name="transcript">
            <div class="transcript-panel">
              <!-- 会议转写内容显示区域 -->
              <div class="transcript-container" ref="transcriptContainer">
                <div v-if="transcriptMessages.length === 0" class="empty-transcript">
                  <i class="el-icon-microphone"></i>
                  <p>暂无会议转写内容</p>
                  <p class="tip">请在会议智能体页面开始语音转写</p>
                </div>
                <div v-else class="transcript-messages">
                  <div
                    v-for="(message, index) in transcriptMessages"
                    :key="index"
                    class="transcript-item"
                  >
                    <div class="message-avatar">
                      <img
                        v-if="message.avatar"
                        :src="message.avatar"
                        :alt="message.nickName || '用户'"
                        class="avatar-img"
                      />
                      <div v-else class="avatar-placeholder">
                        {{ (message.nickName || '用户').charAt(0) }}
                      </div>
                    </div>
                    <div class="message-content">
                      <div class="message-header">
                        <span class="message-name">{{ message.nickName || '用户' }}</span>
                        <span class="message-time">{{ message.time }}</span>
                      </div>
                      <div class="message-text">{{ message.content }}</div>
                    </div>
                  </div>
                </div>

                <!-- 思考过程显示区域 -->
                <div v-if="thinkingProcess" class="thinking-process-section">
                  <div class="section-divider">
                    <span class="divider-text">AI 思考过程</span>
                  </div>
                  <div class="thinking-content">
                    <div class="thinking-header">
                      <i :class="thinkingCompleted ? 'el-icon-check' : 'el-icon-loading'"></i>
                      <span>正在分析会议内容...</span>
                      <span v-if="thinkingCompleted" class="completed-text">(分析完成)</span>
                    </div>
                    <div class="thinking-text">
                      {{ thinkingProcess }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>

        <!-- 底部操作按钮 -->
        <div class="panel-footer" v-if="activeTab === 'transcript'">
          <el-button
            type="primary"
            :loading="generatingMinutes"
            @click="generateMeetingMinutes"
            :disabled="transcriptMessages.length === 0"
          >
            <i class="el-icon-document"></i>
            生成会议纪要
          </el-button>
          <el-button
            v-if="generatingMinutes"
            @click="stopGeneration"
          >
            <i class="el-icon-video-pause"></i>
            停止生成
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TinyEditor from '@/components/TinyEditor/index.vue'
import { getMeetingDetail, addMeeting, updateMeeting, formatMeetingTime } from '@/api/meeting'
import { callWorkflowStream, callXZSStream } from '@/api/common/workflow-stream'

export default {
  name: 'MeetingEditor',
  components: {
    TinyEditor
  },
  data() {
    return {
      // 编辑器相关
      documentContent: '',
      editorHeight: 600,

      // Tab切换
      activeTab: 'ai',

      // AI助手相关
      chatMessages: [],
      aiInputMessage: '',
      aiSending: false,

      // 会议转写相关
      transcriptMessages: [],

      // 思考过程相关
      thinkingProcess: '',
      thinkingCompleted: false,

      // 生成相关
      generatingMinutes: false,
      generationController: null,

      // 流式输出状态
      streamState: { inThink: false, tail: '', thinkingCompleted: false },
      currentStreamingIndex: -1,



      // 会议信息
      meetingInfo: {
        id: null,
        meetingId: null,
        title: '',
        cTime: '',
        room: '',
        jy: '',
        creator: '',
        startTime: '',
        endTime: '',
        status: 0,
        details: []
      }
    }
  },
  mounted() {
    this.initEditor()
    this.initAIChat()
    this.loadTranscriptData()
    this.calculateEditorHeight()
    this.loadMeetingData()

    // 监听窗口大小变化
    window.addEventListener('resize', this.calculateEditorHeight)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.calculateEditorHeight)
    if (this.generationController) {
      this.generationController.abort()
    }
  },
  methods: {
    // 加载会议数据
    async loadMeetingData() {
      // 从路由参数获取会议信息
      const { id, meetingId, title } = this.$route.query

      if (id || meetingId) {
        this.meetingInfo.id = id
        this.meetingInfo.meetingId = meetingId
        this.meetingInfo.title = title || ''

        // 如果有ID，从API加载会议详情
        if (id) {
          try {
            const response = await getMeetingDetail(id)
            if (response.code === 200 && response.data) {
              const meetingData = response.data
              this.meetingInfo = {
                ...this.meetingInfo,
                ...meetingData,
                title: meetingData.title || title || '会议纪要编辑'
              }

              // 设置文档内容
              if (meetingData.jy) {
                this.documentContent = meetingData.jy
              } else {
                this.initEditor()
              }

              // 加载会议详情数据到转写区域
              if (meetingData.details && meetingData.details.length > 0) {
                this.transcriptMessages = meetingData.details.map(detail => ({
                  ...detail,
                  time: detail.createTime,
                  content: detail.content,
                  nickName: detail.nickName,
                  avatar: detail.avatar
                }))
              }
            }
          } catch (error) {
            console.error('加载会议详情失败:', error)
            this.$message.error('加载会议详情失败')
            this.initEditor()
          }
        } else {
          // 新建会议，初始化编辑器
          this.initEditor()
        }

        // 根据会议ID加载对应的转写数据（如果没有从API加载到）
        if (!this.transcriptMessages.length) {
          this.loadTranscriptDataById(meetingId || id)
        }

        // 更新页面标题
        if (this.meetingInfo.title) {
          document.title = `${this.meetingInfo.title} - 会议纪要编辑`
        }
      } else {
        // 新建会议
        this.initEditor()
      }
    },

    // 根据ID加载转写数据
    loadTranscriptDataById(meetingId) {
      // 尝试从localStorage加载特定会议的转写数据
      const savedTranscript = localStorage.getItem(`meetingTranscript_${meetingId}`)
      if (savedTranscript) {
        try {
          this.transcriptMessages = JSON.parse(savedTranscript)
          return
        } catch (e) {
          console.error('加载会议转写数据失败:', e)
        }
      }

      // 如果没有特定会议的数据，使用通用的转写数据
      this.loadTranscriptData()
    },

    // 根据ID加载已保存的文档
    loadSavedDocument(id) {
      if (id) {
        const savedDoc = localStorage.getItem(`meetingDocument_${id}`)
        if (savedDoc) {
          this.documentContent = savedDoc
          return
        }
      }

      // 如果没有保存的文档，使用默认模板
      this.initEditor()
    },

    // 初始化编辑器
    initEditor() {
      const meetingTitle = this.meetingInfo.title || '会议纪要'
      this.documentContent = ''
    },

    // 初始化AI聊天
    initAIChat() {
      this.chatMessages = [
        {
          type: 'ai',
          content: '您好！我是AI会议助手，我可以帮您分析会议内容、生成会议纪要、回答相关问题。请告诉我您需要什么帮助？',
          time: this.getCurrentTime(),
          contentType: 'text'
        }
      ]
    },

    // 加载转写数据（从localStorage或其他来源）
    loadTranscriptData() {
      // 这里可以从localStorage或其他地方加载会议转写数据
      const savedTranscript = localStorage.getItem('meetingTranscript')
      if (savedTranscript) {
        try {
          this.transcriptMessages = JSON.parse(savedTranscript)
        } catch (e) {
          console.error('加载转写数据失败:', e)
        }
      }

    },

    // 计算编辑器高度
    calculateEditorHeight() {
      const windowHeight = window.innerHeight
      this.editorHeight = windowHeight - 200 // 减去头部和其他元素的高度
    },

    // 处理编辑器内容变化
    handleContentChange(data) {
      // 自动保存到localStorage，根据会议ID区分
      const saveKey = this.meetingInfo.id
        ? `meetingDocument_${this.meetingInfo.id}`
        : 'meetingDocument'
      localStorage.setItem(saveKey, this.documentContent)
    },

    // 处理TinyEditor导出Word事件
    handleExportWord(data) {
      console.log('TinyEditor导出Word:', data)
      if (data.success) {
        this.$message.success(`Word文档导出成功: ${data.filename}`)
      } else {
        this.$message.error('Word文档导出失败: ' + (data.error || '未知错误'))
      }
    },

    // 保存文档
    async saveDocument() {
      try {
        const meetingData = {
          title: this.meetingInfo.title || '会议纪要',
          room: this.meetingInfo.room || '',
          jy: this.documentContent,
          creator: this.meetingInfo.creator || '',
          startTime: this.meetingInfo.startTime || '',
          endTime: this.meetingInfo.endTime || '',
          status: this.meetingInfo.status || 0,
          details: this.transcriptMessages.map(msg => ({
            nickName: msg.nickName || '用户',
            avatar: msg.avatar || '',
            content: msg.content
          }))
        }

        let response
        if (this.meetingInfo.id) {
          // 更新现有会议
          meetingData.id = this.meetingInfo.id
          response = await updateMeeting(meetingData)
        } else {
          // 新增会议
          response = await addMeeting(meetingData)
        }

        if (response.code === 200) {
          this.$message.success('文档已保存')

          // 如果是新增，更新会议ID
          if (!this.meetingInfo.id && response.data) {
            this.meetingInfo.id = response.data.id || response.data
            // 更新路由参数
            this.$router.replace({
              path: this.$route.path,
              query: {
                ...this.$route.query,
                id: this.meetingInfo.id
              }
            })
          }
        } else {
          this.$message.error(response.msg || '保存失败')
        }

        // 同时保存到localStorage作为备份
        const saveKey = this.meetingInfo.id
          ? `meetingDocument_${this.meetingInfo.id}`
          : 'meetingDocument'
        localStorage.setItem(saveKey, this.documentContent)

      } catch (error) {
        console.error('保存文档失败:', error)
        this.$message.error('保存失败')

        // 失败时仍然保存到localStorage
        const saveKey = this.meetingInfo.id
          ? `meetingDocument_${this.meetingInfo.id}`
          : 'meetingDocument'
        localStorage.setItem(saveKey, this.documentContent)
      }
    },


    // 发送AI消息
    async sendAIMessage() {
      if (!this.aiInputMessage.trim() || this.aiSending) return

      const userMessage = {
        type: 'user',
        content: this.aiInputMessage,
        time: this.getCurrentTime()
      }

      this.chatMessages.push(userMessage)
      const userInput = this.aiInputMessage
      this.aiInputMessage = ''
      this.aiSending = true

      try {
        await this.streamChat(userInput)
      } catch (e) {
        console.error('AI对话失败:', e)
        this.$message.error('AI服务调用失败')
      } finally {
        this.aiSending = false
        this.$nextTick(() => this.scrollChatToBottom())
      }
    },

    // 流式AI对话
    async streamChat(query) {
      // 插入AI占位消息
      const placeholder = {
        type: 'ai',
        contentType: 'stream',
        content: {
          thinking: '',
          answer: '',
          thinkingCompleted: false
        },
        time: this.getCurrentTime()
      }
      this.chatMessages.push(placeholder)
      const aiMessageIndex = this.chatMessages.length - 1
      this.$nextTick(() => this.scrollChatToBottom())

      // 创建取消控制器
      const controller = new AbortController()

      try {
        // 准备会议数据
        const meetingData = {
          title: this.meetingInfo.title || '会议纪要',
          time: this.meetingInfo.startTime || this.getCurrentDate(),
          sponsor: this.meetingInfo.creator || '系统',
          content: this.transcriptMessages.length > 0
            ? this.transcriptMessages.map(msg => `${msg.nickName}: ${msg.content}`).join('\n')
            : '暂无会议内容',
          query: query
        }

        // 调用AI小助手接口
        await callXZSStream(meetingData, {
          controller: controller,
          onProgress: (_, fullContent) => {
            // 处理思考过程和回答内容
            this.processAIStreamContent(fullContent, aiMessageIndex)
            this.scrollChatToBottom()
          },
          onThought: (thought) => {
            // 处理思考过程回调
            const message = this.chatMessages[aiMessageIndex]
            if (message && message.contentType === 'stream') {
              message.content.thinking = thought
              message.content.thinkingCompleted = false
              this.$forceUpdate()
              this.scrollChatToBottom()
            }
          },
          onComplete: (finalContent) => {
            // 回答完成
            this.processAIStreamContent(finalContent, aiMessageIndex, true)
            // 标记思考过程完成
            const message = this.chatMessages[aiMessageIndex]
            if (message && message.contentType === 'stream' && message.content.thinking) {
              message.content.thinkingCompleted = true
            }
            this.$forceUpdate()
          },
          onError: (error) => {
            console.error('AI问答失败:', error)
            this.chatMessages[aiMessageIndex] = {
              type: 'ai',
              contentType: 'text',
              content: '抱歉，AI服务暂时不可用，请稍后再试。',
              time: this.getCurrentTime()
            }
            this.$forceUpdate()
          }
        })

      } catch (error) {
        console.error('AI对话失败:', error)
        this.chatMessages[aiMessageIndex] = {
          type: 'ai',
          contentType: 'text',
          content: '抱歉，AI服务暂时不可用，请稍后再试。',
          time: this.getCurrentTime()
        }
        this.$forceUpdate()
      }
    },

    // 处理AI流式内容，分离思考过程和回答
    processAIStreamContent(fullContent, messageIndex, isComplete = false) {
      const message = this.chatMessages[messageIndex]
      if (!message || message.contentType !== 'stream') return



      let processedContent = fullContent
      let currentThinking = ''
      let answerContent = ''

      // 检查是否有思考标签
      const openThinkIndex = processedContent.lastIndexOf('<think>')
      const closeThinkIndex = processedContent.lastIndexOf('</think>')



      if (openThinkIndex !== -1) {
        if (closeThinkIndex === -1 || openThinkIndex > closeThinkIndex) {
          // 思考过程正在进行中
          currentThinking = processedContent.substring(openThinkIndex + 7) // 7 是 '<think>' 的长度
          message.content.thinkingCompleted = false

          // 从正文中移除未完成的思考部分
          answerContent = processedContent.substring(0, openThinkIndex)


        } else {
          // 思考过程已完成
          const thinkingMatch = processedContent.match(/<think>(.*?)<\/think>/s)
          if (thinkingMatch) {
            currentThinking = thinkingMatch[1].trim()
            message.content.thinkingCompleted = true

          }

          // 从正文中移除所有思考过程
          answerContent = processedContent.replace(/<think>.*?<\/think>/gs, '')
        }

        // 更新思考内容
        message.content.thinking = currentThinking
      } else {
        // 没有思考标签，全部作为回答内容
        answerContent = processedContent

      }

      // 清理markdown代码块标记
      answerContent = this.cleanMarkdownCodeBlocks(answerContent)

      // 更新回答内容
      message.content.answer = answerContent.trim()

      // 如果完成了，确保思考过程标记为完成
      if (isComplete && message.content.thinking) {
        message.content.thinkingCompleted = true
      }

      this.$forceUpdate()
    },



    // 清空AI对话
    clearAIChat() {
      this.$confirm('确定要清空所有对话记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.initAIChat()
        this.$message.success('对话记录已清空')
      }).catch(() => {
        // 用户取消
      })
    },

    // 生成会议纪要（底部按钮触发）
    async generateMeetingMinutes() {
      if (this.transcriptMessages.length === 0) {
        this.$message.warning('暂无会议内容可生成纪要')
        return
      }

      this.generatingMinutes = true

      // 重置思考过程状态
      this.thinkingProcess = ''
      this.thinkingCompleted = false

      try {
        // 准备请求参数
        const requestData = {
          title: this.meetingInfo.title,
          time: this.meetingInfo.startTime,
          sponsor: this.meetingInfo.creator,
          content: JSON.stringify(this.transcriptMessages),
          meeting: '会议'
        }

        // 切换到会议原文tab以显示思考过程
        this.activeTab = 'transcript'

        // 调用流式输出接口
        await this.callWorkflowAPI(requestData)

      } catch (error) {
        console.error('生成会议纪要失败:', error)
        this.$message.error('生成会议纪要失败')
      } finally {
        this.generatingMinutes = false
      }
    },

    // 调用工作流API进行流式输出
    async callWorkflowAPI(requestData) {
      const controller = new AbortController()
      this.generationController = controller

      try {
        this.currentStreamingIndex = -1
        this.streamState = { inThink: false, tail: '', thinkingCompleted: false }

        // 清空编辑器内容，准备显示新生成的内容
        this.documentContent = ''

        // 使用 callWorkflowStream 接口
        await callWorkflowStream(requestData, {
          user: 'meeting-user',
          controller: controller,
          onProgress: (_, fullContent) => {
            // 处理思考过程和正文内容
            this.processWorkflowStreamContent(fullContent)
          },
          onComplete: (finalContent) => {
            // 工作流结束
            this.processWorkflowStreamContent(finalContent, true)
            this.thinkingCompleted = true

            // 滚动到思考过程区域
            this.$nextTick(() => {
              this.scrollTranscriptToBottom()
            })
          },
          onError: (error) => {
            console.error('流式工作流调用失败:', error)
            this.$message.error('生成失败: ' + error.message)
            this.thinkingProcess = '生成失败: ' + error.message
            this.thinkingCompleted = true
          }
        })

      } catch (error) {
        if (error.name === 'AbortError') {
          // 生成已取消
          this.thinkingProcess += '\n\n[生成已取消]'
          this.thinkingCompleted = true
        } else {
          console.error('调用工作流失败:', error)
          this.$message.error('生成失败: ' + error.message)
          this.thinkingProcess = '生成失败: ' + error.message
          this.thinkingCompleted = true
        }
      } finally {
        this.generationController = null
        this.currentStreamingIndex = -1
      }
    },

    // 停止生成
    stopGeneration() {
      if (this.generationController) {
        this.generationController.abort()
        this.generationController = null
      }
      this.generatingMinutes = false
      this.$message.info('已停止生成')
    },

    // 处理工作流流式内容，分离思考过程和正文
    processWorkflowStreamContent(fullContent, isComplete = false) {
      let processedContent = fullContent
      let currentThinking = ''
      let answerContent = ''

      // 检查是否有思考标签
      const openThinkIndex = processedContent.lastIndexOf('<think>')
      const closeThinkIndex = processedContent.lastIndexOf('</think>')

      if (openThinkIndex !== -1) {
        if (closeThinkIndex === -1 || openThinkIndex > closeThinkIndex) {
          // 思考过程正在进行中
          currentThinking = processedContent.substring(openThinkIndex + 7) // 7 是 '<think>' 的长度
          this.thinkingCompleted = false

          // 从正文中移除未完成的思考部分
          answerContent = processedContent.substring(0, openThinkIndex)
        } else {
          // 思考过程已完成
          const thinkingMatch = processedContent.match(/<think>(.*?)<\/think>/s)
          if (thinkingMatch) {
            currentThinking = thinkingMatch[1].trim()
            this.thinkingCompleted = true
          }

          // 从正文中移除所有思考过程
          answerContent = processedContent.replace(/<think>.*?<\/think>/gs, '')
        }

        // 更新思考内容
        this.thinkingProcess = currentThinking
      } else {
        // 没有思考标签，全部作为回答内容
        answerContent = processedContent
      }

      // 清理markdown代码块标记
      answerContent = this.cleanMarkdownCodeBlocks(answerContent)

      // 更新编辑器内容（只包含正文）
      this.documentContent = answerContent.trim()

      // 如果完成了，确保思考过程标记为完成
      if (isComplete && this.thinkingProcess) {
        this.thinkingCompleted = true
      }

      // 滚动到底部
      this.$nextTick(() => {
        this.scrollTranscriptToBottom()
      })
    },

    // 滚动转写区域到底部
    scrollTranscriptToBottom() {
      const container = this.$refs.transcriptContainer
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    },



    // 格式化答案HTML
    formatAnswerHtml(text) {
      if (!text) return ''

      let html = String(text)
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;')

      // 简单格式化
      html = html.replace(/\n/g, '<br>')
      html = html.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')

      return html
    },

    // 滚动聊天到底部
    scrollChatToBottom() {
      this.$nextTick(() => {
        const container = this.$refs.chatContainer
        if (container) {
          container.scrollTop = container.scrollHeight
        }
      })
    },

    // 获取当前时间
    getCurrentTime() {
      const now = new Date()
      return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`
    },

    // 获取当前日期
    getCurrentDate() {
      const now = new Date()
      return `${now.getFullYear()}年${(now.getMonth() + 1).toString().padStart(2, '0')}月${now.getDate().toString().padStart(2, '0')}日`
    },

    // 清理markdown代码块标记
    cleanMarkdownCodeBlocks(content) {
      if (!content || typeof content !== "string") {
        return content;
      }



      // 只在确实是markdown代码块的情况下才清理
      let cleanedContent = content;

      // 移除开头的 ```html 标记（只在行首）
      cleanedContent = cleanedContent.replace(/^```html\s*/i, "");

      // 移除结尾的 ``` 标记（只在行尾）
      cleanedContent = cleanedContent.replace(/\s*```\s*$/i, "");

      // 移除其他可能的markdown代码块标记，但要小心不要误删HTML内容
      // 只移除明显的markdown代码块标记
      cleanedContent = cleanedContent.replace(/^```[a-zA-Z]*\s*/gm, "");
      cleanedContent = cleanedContent.replace(/\s*```\s*$/gm, "");



      return cleanedContent;
    },

    // 返回上一页
    goBack() {
      this.$router.push({ path: '/meeting/list' })
    }
  }
}
</script>

<style lang="scss" scoped>
.meeting-editor-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.main-content {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 16px;
  overflow: hidden;
}

// 左侧编辑器
.left-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;
    background: #fff;

    .header-left {
      display: flex;
      align-items: center;
      gap: 12px;

      .back-button {
        color: #606266;
        border-color: #dcdfe6;

        &:hover {
          color: #409eff;
          border-color: #c6e2ff;
          background-color: #ecf5ff;
        }
      }

      h3 {
        margin: 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .editor-actions {
      display: flex;
      gap: 8px;
    }
  }

  .editor-container {
    flex: 1;
    overflow: hidden;
  }
}

// 右侧面板
.right-panel {
  width: 400px;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .panel-header {
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;
    background: #fff;

    h3 {
      margin: 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }
  }

  .meeting-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    ::v-deep .el-tabs__header {
      margin: 0;
      padding: 0 20px;
      border-bottom: 1px solid #ebeef5;
    }

    ::v-deep .el-tabs__content {
      flex: 1;
      overflow: hidden;
      padding: 0;
    }

    ::v-deep .el-tab-pane {
      height: 100%;
      overflow: hidden;
    }
  }

  .panel-footer {
    padding: 16px 20px;
    border-top: 1px solid #ebeef5;
    background: #fff;
    display: flex;
    gap: 8px;
  }
}

// AI助手面板
.ai-assistant-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .chat-container {
    flex: 1;
    overflow-y: auto;
    padding: 16px 20px;
    background: #f8f9fa;

    .chat-messages {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
  }

  .ai-quick-actions {
    padding: 12px 20px;
    border-top: 1px solid #ebeef5;
    background: #f8f9fa;
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
    justify-content: flex-end;
  }

  .ai-input-container {
    padding: 16px 20px;
    border-top: 1px solid #ebeef5;
    background: #fff;

    .input-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 8px;

      .send-tip {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}



// 消息样式
.message-item {
  .message-content {
    display: flex;
    align-items: flex-start;
    gap: 8px;

    &.user-message {
      flex-direction: row-reverse;
    }
  }

  .ai-avatar, .user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    i {
      font-size: 16px;
      color: #fff;
    }
  }

  .ai-avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .user-avatar {
    background: #409eff;
  }

  .message-text {
    max-width: 280px;
    background: #fff;
    padding: 12px 16px;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    word-wrap: break-word;
    line-height: 1.5;
    position: relative;

    &.user-text {
      background: #409eff;
      color: #fff;
    }

    .message-time {
      font-size: 11px;
      color: #c0c4cc;
      margin-top: 4px;
      text-align: right;
    }

    &.user-text .message-time {
      color: rgba(255, 255, 255, 0.8);
    }

    .ai-response {
      white-space: pre-wrap;
      word-wrap: break-word;
      line-height: 1.6;
    }
  }
}

// 流式输出样式
.stream-block {
  margin-bottom: 12px;

  h4 {
    color: #303133;
    font-size: 13px;
    font-weight: 600;
    margin: 0 0 6px 0;
    display: flex;
    align-items: center;

    i {
      margin-right: 4px;
      color: #409eff;
      font-size: 12px;
    }
  }

  .thinking-text {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 8px;
    min-height: 40px;
    font-size: 12px;
    line-height: 1.4;
    color: #909399;
    white-space: pre-wrap;
    word-wrap: break-word;
  }
}

// 转写面板
.transcript-panel {
  height: 100%;
  overflow: hidden;

  .transcript-container {
    height: 100%;
    overflow-y: auto;
    padding: 16px 20px;
  }

  .empty-transcript {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #909399;

    i {
      font-size: 48px;
      margin-bottom: 16px;
      color: #dcdfe6;
    }

    p {
      margin: 4px 0;
      font-size: 14px;

      &.tip {
        font-size: 12px;
        color: #c0c4cc;
      }
    }
  }

  .transcript-messages {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .transcript-item {
    display: flex;
    gap: 12px;
    align-items: flex-start;

    .message-avatar {
      flex-shrink: 0;
      width: 40px;
      height: 40px;

      .avatar-img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
      }

      .avatar-placeholder {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 500;
        font-size: 16px;
      }
    }

    .message-content {
      flex: 1;
      min-width: 0;

      .message-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 4px;

        .message-name {
          font-weight: 500;
          color: #303133;
          font-size: 14px;
        }

        .message-time {
          font-size: 12px;
          color: #909399;
        }
      }

      .message-text {
        background: #f8f9fa;
        padding: 12px 16px;
        border-radius: 12px;
        border-top-left-radius: 4px;
        color: #606266;
        line-height: 1.5;
        font-size: 14px;
        word-wrap: break-word;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }
    }
  }

  // 思考过程区域样式
  .thinking-process-section {
    margin-top: 24px;

    .section-divider {
      display: flex;
      align-items: center;
      margin: 20px 0 16px 0;

      &::before,
      &::after {
        content: '';
        flex: 1;
        height: 1px;
        background: #e4e7ed;
      }

      .divider-text {
        padding: 0 16px;
        color: #909399;
        font-size: 13px;
        font-weight: 500;
        background: #fff;
      }
    }

    .thinking-content {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 16px;

      .thinking-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        color: #606266;
        font-size: 14px;
        font-weight: 500;

        i {
          margin-right: 8px;
          color: #409eff;
          font-size: 14px;

          &.el-icon-loading {
            animation: rotating 2s linear infinite;
          }

          &.el-icon-check {
            color: #67c23a;
          }
        }

        .completed-text {
          margin-left: 8px;
          color: #67c23a;
          font-size: 12px;
          font-weight: normal;
        }
      }

      .thinking-text {
        background: #fff;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        padding: 12px;
        min-height: 60px;
        font-size: 13px;
        line-height: 1.5;
        color: #606266;
        white-space: pre-wrap;
        word-wrap: break-word;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      }
    }
  }
}

// 动画
@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .right-panel {
    width: 350px;
  }
}

@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
    gap: 12px;
  }

  .right-panel {
    width: 100%;
    height: 400px;
  }
}
</style>
