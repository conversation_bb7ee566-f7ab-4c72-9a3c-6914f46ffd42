/**
 * 通用工作流流式接口
 * 基于 http://192.168.110.31/v1/workflows/run 的流式调用封装
 */

// API 配置
const WORKFLOW_CONFIG = {
  url: process.env.VUE_APP_GONGWEN_API_URL,
  apiKey: process.env.VUE_APP_HUIYI_API_KEY,
  timeout: 300000, // 5分钟超时
};

/**
 * 通用流式工作流调用接口
 * @param {Object} inputs - 输入参数对象
 * @param {Object} options - 配置选项
 * @param {string} options.user - 用户标识，默认为 'api-user'
 * @param {Function} options.onProgress - 流式数据回调函数 (chunk, fullContent) => void
 * @param {Function} options.onComplete - 完成回调函数 (finalContent) => void
 * @param {Function} options.onError - 错误回调函数 (error) => void
 * @param {Function} options.onThought - 思考过程回调函数 (thought) => void
 * @param {AbortController} options.controller - 用于取消请求的控制器
 * @returns {Promise<Object>} 生成结果
 */
export async function callWorkflowStream(inputs, options = {}, apiKey) {
  const {
    user = "api-user",
    onProgress,
    onComplete,
    onError,
    onThought,
    controller,
  } = options;

  try {
    // 验证输入参数
    if (!inputs || typeof inputs !== "object") {
      throw new Error("inputs 参数必须是一个对象");
    }

    // 组装请求体
    const requestData = {
      inputs,
      response_mode: "streaming",
      user,
    };

    console.log("发送流式工作流请求:", JSON.stringify(requestData, null, 2));

    // 发送请求
    const response = await fetch(WORKFLOW_CONFIG.url, {
      method: "POST",
      headers: {
        Authorization: apiKey
          ? `Bearer ${apiKey}`
          : `Bearer ${WORKFLOW_CONFIG.apiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestData),
      signal: controller?.signal,
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = "";
    let fullContent = "";

    try {
      while (true) {
        const { done, value } = await reader.read();

        if (done) break;

        // 解码数据块
        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split("\n");
        buffer = lines.pop() || ""; // 保留不完整的行

        for (const line of lines) {
          if (!line.trim()) continue; // 跳过空行

          let data = line;
          // 处理 SSE 格式的数据
          if (line.startsWith("data: ")) {
            data = line.slice(6);
          }

          if (data === "[DONE]" || data.trim() === "") {
            // 流式数据结束
            if (onComplete) {
              onComplete(fullContent);
            }
            break;
          }

          try {
            const parsed = JSON.parse(data);
            console.log("接收到流式数据:", parsed);

            // 处理文本块 - 正文内容
            if (
              parsed.event === "text_chunk" &&
              parsed.data &&
              parsed.data.text
            ) {
              const deltaContent = parsed.data.text;
              fullContent += deltaContent;

              // 调用进度回调
              if (onProgress) {
                onProgress(deltaContent, fullContent);
              }
            }
            // 处理思考过程
            else if (
              parsed.event === "agent_thought" &&
              parsed.data &&
              parsed.data.thought
            ) {
              if (onThought) {
                onThought(parsed.data.thought);
              }
            }
            // 处理工作流开始
            else if (parsed.event === "workflow_started") {
              console.log("工作流开始");
            }
            // 处理工作流完成
            else if (parsed.event === "workflow_finished") {
              console.log("工作流完成");
              if (onComplete) {
                onComplete(fullContent);
              }
              break;
            }
            // 兼容其他消息格式
            else if (
              parsed.event === "message" ||
              parsed.event === "message_end"
            ) {
              if (parsed.data && parsed.data.answer) {
                const deltaContent = parsed.data.answer;
                fullContent += deltaContent;

                if (onProgress) {
                  onProgress(deltaContent, fullContent);
                }
              }
              if (parsed.event === "message_end") {
                if (onComplete) {
                  onComplete(fullContent);
                }
                break;
              }
            }
          } catch (parseError) {
            console.warn("解析流式数据失败:", parseError, "Line:", line);
          }
        }
      }

      // 如果流式处理正常结束但没有收到完成事件，调用完成回调
      if (onComplete && fullContent && !controller?.signal?.aborted) {
        onComplete(fullContent);
      }

      return {
        success: true,
        data: {
          content: fullContent,
          isStreaming: true,
        },
      };
    } finally {
      reader.releaseLock();
    }
  } catch (error) {
    console.error("流式工作流调用失败:", error.message);

    if (onError) {
      onError(error);
    }

    return {
      success: false,
      error: {
        message: error.message,
        name: error.name,
      },
    };
  }
}

/**
 * 公文生成流式接口
 * @param {Object} params - 公文生成参数
 * @param {string} params.type - 公文类型
 * @param {string} params.title - 公文标题
 * @param {string} params.requirements - 内容要求
 * @param {string} params.outline - 大纲
 * @param {number} params.min - 最小字数
 * @param {number} params.max - 最大字数
 * @param {Object} options - 配置选项
 * @returns {Promise<Object>} 生成结果
 */
export async function generateGongwenStream(params, options = {}) {
  const inputs = {
    type: params.type,
    title: params.title,
    outline: params.outline,
    requirements: params.requirements,
    min: params.min,
    max: params.max,
    gongwen: "公文",
  };

  return callWorkflowStream(inputs, {
    user: "gongwen-user",
    ...options,
  });
}

/**
 * 会议纪要生成流式接口
 * @param {Object} params - 会议纪要生成参数
 * @param {string} params.title - 会议标题
 * @param {string} params.time - 会议时间
 * @param {string} params.sponsor - 会议发起人
 * @param {Array|string} params.content - 会议内容
 * @param {Object} options - 配置选项
 * @returns {Promise<Object>} 生成结果
 */
export async function generateMeetingMinutesStream(params, options = {}) {
  const inputs = {
    title: params.title,
    time: params.time,
    sponsor: params.sponsor,
    content:
      typeof params.content === "string"
        ? params.content
        : JSON.stringify(params.content),
    meeting: "会议",
  };

  return callWorkflowStream(inputs, {
    user: "meeting-user",
    ...options,
  });
}

/**
 * 通用文本生成流式接口
 * @param {Object} inputs - 自定义输入参数
 * @param {Object} options - 配置选项
 * @returns {Promise<Object>} 生成结果
 */
export async function generateTextStream(inputs, options = {}) {
  return callWorkflowStream(inputs, {
    user: "text-user",
    ...options,
  });
}

/**
 * AI小助手问答流式接口
 * @param {Object} params - AI问答参数
 * @param {string} params.title - 会议标题
 * @param {string} params.time - 会议时间
 * @param {string} params.sponsor - 会议发起人
 * @param {Array|string} params.content - 会议内容
 * @param {string} params.query - 用户的问题
 * @param {Object} options - 配置选项
 * @returns {Promise<Object>} 生成结果
 */
export async function generateAIAssistantStream(params, options = {}) {
  const inputs = {
    title: params.title,
    time: params.time,
    sponsor: params.sponsor,
    content:
      typeof params.content === "string"
        ? params.content
        : JSON.stringify(params.content),
    "sys.query": params.query, // 用户的问题
  };

  // 使用专门的AI小助手API Key
  const aiAssistantApiKey = process.env.VUE_APP_XZS_KEY;

  return callWorkflowStream(
    inputs,
    {
      user: "ai-assistant-user",
      ...options,
    },
    aiAssistantApiKey
  );
}

/**
 * XZS流式接口
 * @param {Object} params - XZS参数，与generateAIAssistantStream保持一致
 * @param {string} params.title - 会议标题
 * @param {string} params.time - 会议时间
 * @param {string} params.sponsor - 会议发起人
 * @param {Array|string} params.content - 会议内容
 * @param {string} params.query - 用户的问题
 * @param {string} params.conversation_id - 会话ID，可选
 * @param {Object} options - 配置选项
 * @returns {Promise<Object>} 生成结果
 */
export async function callXZSStream(params, options = {}) {
  const { title, time, sponsor, content, query, conversation_id = "" } = params;

  try {
    // 验证必要参数
    if (!query) {
      throw new Error("query 参数是必需的");
    }

    // 使用 XZS API 配置
    const xzsApiUrl = process.env.VUE_APP_XZS_API_KEY; // '/v1/chat-messages'
    const xzsApiKey = process.env.VUE_APP_XZS_KEY; // 'app-4So8okV2nKPILIMG3vDTYdQi'

    // 组装请求体，与AiAssistantDrawer.vue中的格式保持一致
    const requestData = {
      inputs: { ...params, "sys.query": query },
      query,
      response_mode: "streaming",
      conversation_id,
      user: "web-user",
    };

    console.log("发送XZS流式请求:", JSON.stringify(requestData, null, 2));

    // 发送请求
    const response = await fetch(xzsApiUrl, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${xzsApiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestData),
      signal: options.controller?.signal,
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = "";
    let fullContent = "";
    let conversationId = "";
    let messageId = "";
    let taskId = "";

    try {
      while (true) {
        const { done, value } = await reader.read();

        if (done) break;

        // 解码数据块
        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split("\n");
        buffer = lines.pop() || ""; // 保留不完整的行

        for (const line of lines) {
          if (!line.trim()) continue; // 跳过空行

          let data = line;
          // 处理 SSE 格式的数据
          if (line.startsWith("data: ")) {
            data = line.slice(6);
          }

          if (data === "[DONE]" || data.trim() === "") {
            // 流式数据结束
            if (options.onComplete) {
              options.onComplete(fullContent);
            }
            break;
          }

          try {
            const parsed = JSON.parse(data);
            console.log("接收到XZS流式数据:", parsed);

            // 处理文本块 - 正文内容
            if (parsed.event === "message" && parsed.answer) {
              const deltaContent = parsed.answer;
              fullContent += deltaContent;

              // 保存元数据
              if (parsed.conversation_id)
                conversationId = parsed.conversation_id;
              if (parsed.message_id) messageId = parsed.message_id;
              if (parsed.task_id) taskId = parsed.task_id;

              // 调用进度回调
              if (options.onProgress) {
                options.onProgress(deltaContent, fullContent);
              }
            }
            // 处理思考过程
            else if (parsed.event === "agent_thought" && parsed.thought) {
              if (options.onThought) {
                options.onThought(parsed.thought);
              }
            }
            // 处理消息结束
            else if (parsed.event === "message_end") {
              console.log("XZS消息结束");
              if (options.onComplete) {
                options.onComplete(fullContent);
              }
              break;
            }
            // 处理错误
            else if (parsed.event === "error") {
              const error = new Error(parsed.message || "XZS API错误");
              if (options.onError) {
                options.onError(error);
              }
              throw error;
            }
          } catch (parseError) {
            console.warn("解析XZS流式数据失败:", parseError, "Line:", line);
          }
        }
      }

      // 如果流式处理正常结束但没有收到完成事件，调用完成回调
      if (
        options.onComplete &&
        fullContent &&
        !options.controller?.signal?.aborted
      ) {
        options.onComplete(fullContent);
      }

      return {
        success: true,
        data: {
          content: fullContent,
          isStreaming: true,
          conversation_id: conversationId,
          message_id: messageId,
          task_id: taskId,
        },
      };
    } finally {
      reader.releaseLock();
    }
  } catch (error) {
    console.error("XZS流式调用失败:", error.message);

    if (options.onError) {
      options.onError(error);
    }

    return {
      success: false,
      error: {
        message: error.message,
        name: error.name,
      },
    };
  }
}

// 导出配置和主要函数
export default {
  callWorkflowStream,
  generateGongwenStream,
  generateMeetingMinutesStream,
  generateTextStream,
  generateAIAssistantStream,
  callXZSStream,
  WORKFLOW_CONFIG,
};
