<template>
  <div class="mobile-pull-refresh" ref="container">
    <!-- 下拉刷新指示器 -->
    <div 
      class="pull-refresh-indicator" 
      :style="{ transform: `translateY(${indicatorTransform}px)` }"
      :class="{ 'refreshing': isRefreshing }"
    >
      <div class="indicator-content">
        <i 
          class="indicator-icon" 
          :class="indicatorIcon"
        ></i>
        <span class="indicator-text">{{ indicatorText }}</span>
      </div>
    </div>

    <!-- 内容区域 -->
    <div 
      class="pull-refresh-content"
      :style="{ transform: `translateY(${contentTransform}px)` }"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
    >
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MobilePullRefresh',
  props: {
    // 是否正在刷新
    refreshing: {
      type: Boolean,
      default: false
    },
    // 触发刷新的距离
    pullDistance: {
      type: Number,
      default: 60
    },
    // 最大下拉距离
    maxPullDistance: {
      type: Number,
      default: 120
    }
  },
  data() {
    return {
      startY: 0,
      currentY: 0,
      isPulling: false,
      pullOffset: 0,
      isRefreshing: false
    }
  },
  computed: {
    // 指示器位置
    indicatorTransform() {
      return Math.max(0, this.pullOffset - 60);
    },
    // 内容区域位置
    contentTransform() {
      return Math.max(0, this.pullOffset);
    },
    // 指示器图标
    indicatorIcon() {
      if (this.isRefreshing) {
        return 'el-icon-loading';
      } else if (this.pullOffset >= this.pullDistance) {
        return 'el-icon-refresh-right';
      } else {
        return 'el-icon-arrow-down';
      }
    },
    // 指示器文本
    indicatorText() {
      if (this.isRefreshing) {
        return '正在刷新...';
      } else if (this.pullOffset >= this.pullDistance) {
        return '释放即可刷新';
      } else {
        return '下拉可以刷新';
      }
    }
  },
  watch: {
    refreshing(val) {
      this.isRefreshing = val;
      if (!val) {
        // 刷新完成，重置状态
        this.resetPull();
      }
    }
  },
  methods: {
    handleTouchStart(event) {
      // 检查是否在顶部
      const scrollTop = this.$refs.container.scrollTop;
      if (scrollTop > 0) return;

      this.startY = event.touches[0].clientY;
      this.isPulling = true;
    },
    
    handleTouchMove(event) {
      if (!this.isPulling || this.isRefreshing) return;

      event.preventDefault();
      this.currentY = event.touches[0].clientY;
      const deltaY = this.currentY - this.startY;

      if (deltaY > 0) {
        // 下拉时应用阻尼效果
        this.pullOffset = Math.min(
          deltaY * 0.5, 
          this.maxPullDistance
        );
      }
    },
    
    handleTouchEnd() {
      if (!this.isPulling) return;

      this.isPulling = false;

      if (this.pullOffset >= this.pullDistance && !this.isRefreshing) {
        // 触发刷新
        this.isRefreshing = true;
        this.$emit('refresh');
      } else {
        // 重置位置
        this.resetPull();
      }
    },
    
    resetPull() {
      this.pullOffset = 0;
      this.startY = 0;
      this.currentY = 0;
      this.isPulling = false;
      
      // 延迟重置刷新状态，让动画完成
      setTimeout(() => {
        this.isRefreshing = false;
      }, 300);
    }
  }
}
</script>

<style lang="scss" scoped>
.mobile-pull-refresh {
  position: relative;
  height: 100%;
  overflow: hidden;

  .pull-refresh-indicator {
    position: absolute;
    top: -60px;
    left: 0;
    right: 0;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f7fa;
    z-index: 10;
    transition: transform 0.3s ease;

    &.refreshing {
      transform: translateY(60px) !important;
    }

    .indicator-content {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #409eff;

      .indicator-icon {
        font-size: 16px;
        
        &.el-icon-loading {
          animation: rotate 1s linear infinite;
        }
      }

      .indicator-text {
        font-size: 14px;
      }
    }
  }

  .pull-refresh-content {
    height: 100%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    transition: transform 0.3s ease;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
