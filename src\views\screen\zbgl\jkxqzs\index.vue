<template>
  <div class="app-container">
    <el-form
      :inline="true"
      :model="dataForm"
      ref="dataForm"
      v-show="showSearch"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item prop="bm">
        <el-input
          v-model="dataForm.bm"
          placeholder="指标编码"
          clearable
        ></el-input>
      </el-form-item>
      <!-- <el-form-item prop="mch1">
        <el-cascader
          v-model="dataForm.mch1"
          :show-all-levels="true"
          placeholder="所属页面"
          :options="ymzts"
          :props="{
            children: 'children',
            label: 'dataName',
            value: 'dataCode',
            checkStrictly: true,
            multiple: false,
          }"
          clearable
          filterable
        />
      </el-form-item> -->
      <el-form-item prop="mch2">
        <el-input
          v-model="dataForm.mch2"
          placeholder="指标概述"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-table :data="dataList" v-loading="dataListLoading" style="width: 100%">
      <el-table-column
        prop="bm"
        header-align="center"
        align="center"
        label="指标编码"
      ></el-table-column>
      <!-- <el-table-column
        prop="mch1"
        header-align="center"
        align="center"
        label="所属页面"
      ></el-table-column> -->
      <el-table-column
        prop="mch2"
        header-align="center"
        align="center"
        label="指标概述"
      ></el-table-column>
      <!-- <el-table-column
        prop="url"
        header-align="center"
        align="center"
        label="接口URL"
      ></el-table-column>
      <el-table-column
        prop="qqfs"
        header-align="center"
        align="center"
        label="请求方式"
      ></el-table-column> -->
      <el-table-column
        header-align="center"
        align="center"
        width="240"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['screen:zbgl:edit']"
            type="text"
            size="small"
            @click="handleDetail(scope.row.id, 'cat')"
          >
            查看详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="pageNum"
      :limit.sync="pageSize"
      @pagination="getDataList"
    />

    <!-- 弹窗 -->
    <zb-api
      v-if="zbApiVisible"
      ref="zbApi"
      @refreshDataList="getDataList"
    ></zb-api>
  </div>
</template>

<script>
import {
  findZbDataList,
  getDpztList,
  zbdcwd,
} from '@/api/screen/zbgl'
import zbApizb from '../zbgl-api-view.vue'
import { listToTree } from '@/utils/Tree'

export default {
  data() {
    return {
      dataForm: {
        bm: '',
        mch1: '',
        mch2: '',
      },
      ymzts: [
        {
          dataName: '',
          dataCode: '',
        },
      ],
      // 显示搜索条件
      showSearch: true,
      dataList: [],
      total: 0,
      pageNum: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      //弹窗
      zbApiVisible: false,
    }
  },
  components: {
    zbApi: zbApizb,
  },
  mounted() {
    this.getDpztList()
  },
  created() {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      findZbDataList({
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        bm: this.dataForm.bm,
        mch1: this.dataForm.mch1,
        mch2: this.dataForm.mch2,
        sjly: this.dataForm.sjly,
        jb: this.dataForm.jb,
      }).then((response) => {
        this.dataList = response.rows
        this.total = response.total
        this.dataListLoading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getDataList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('dataForm')
      this.handleQuery()
    },

    // 查看接口详情
    handleDetail(id) {
      this.dataListLoading = true
      var bm = ''
      var mch1 = ''
      var mch2 = ''
      if (id.length === 0) {
        bm = this.dataForm.bm
        mch1 = this.dataForm.mch1
        mch2 = this.dataForm.mch2
      }
      zbdcwd({
        page: this.pageNum,
        limit: this.pageSize,
        id: id,
        bm: bm,
        mch1: mch1,
        mch2: mch2,
      }).then((response) => {
        this.zbApiVisible = true
        this.$nextTick(() => {
          this.$refs.zbApi.init(response.data, id, bm, mch1, mch2)
        })
        this.dataListLoading = false
      })
    },
    // 获取页面专题
    async getDpztList() {
      await getDpztList({}).then((response) => {
        this.ymzts = listToTree('id', 'parentCode', response.data)
      })
    },
  },
}
</script>
