<template>
  <div class="meeting-agent-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="page-title">
        <i class="el-icon-cpu"></i>
        <h1>智能会议助手</h1>
      </div>
      <div class="page-description">
        请按【开始转写】按钮开始转写语音。
      </div>
    </div>

    <!-- 会议控制栏 -->
    <div class="meeting-controls">
      <el-button
        type="text"
        class="action-btn"
        :class="{ 'recording': isRecording }"
        @click="toggleRecording"
        size="large"
      >
        <i :class="isRecording ? 'el-icon-video-pause' : 'el-icon-microphone'"></i>
        {{ isRecording ? '停止转写' : '开始转写' }}
      </el-button>
      <el-button
        type="primary"
        class="summary-btn"
        size="large"
        @click="showMeetingSummary"
      >
        <i class="el-icon-document"></i>
        总结会议
      </el-button>
    </div>

    <!-- 聊天消息区域 -->
    <!-- 转写内容面板 -->
    <!-- <div class="transcript-panel">
      <div class="panel-header">
        <i class="el-icon-microphone"></i>
        <span>转写内容</span>
        <span class="status-tag" :class="{ active: isRecording }">{{ isRecording ? '录音中' : '未开始' }}</span>
      </div>
      <div class="panel-body" ref="transcriptPanel">
        <div v-if="transcriptMessages.length === 0 && !currentTranscript" class="empty-tip">暂无转写内容</div>
        <div v-for="(item, idx) in transcriptMessages" :key="'ts-'+idx" class="ts-item">
          <span class="ts-time">{{ item.time }}</span>
          <span class="ts-text">{{ item.content }}</span>
        </div>
        <div v-if="currentTranscript" class="ts-item interim">
          <span class="ts-time">实时</span>
          <span class="ts-text">{{ currentTranscript }}</span>
        </div>
      </div>
    </div> -->

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧：会议转写区域 -->
      <div class="left-panel">
        <div class="panel-header">
          <h3>会议转写</h3>
          <div class="status-indicator" :class="{ 'recording': isRecording }">
            <i :class="isRecording ? 'el-icon-microphone' : 'el-icon-video-pause'"></i>
            {{ isRecording ? '录音中' : '未开始' }}
          </div>
        </div>

        <div class="transcript-container" ref="transcriptContainer">
          <div v-if="transcriptMessages.length === 0 && !currentTranscript" class="empty-transcript">
            <i class="el-icon-microphone"></i>
            <p>暂无转写内容</p>
            <p class="tip">请点击"开始转写"按钮开始语音转写</p>
          </div>

          <div v-else class="transcript-messages">
            <div
              v-for="(message, index) in allMessages"
              :key="index"
              class="transcript-item"
              :class="{
                'system-message': message.type === 'system',
                'streaming-message': message.isStreaming
              }"
            >
              <div class="transcript-avatar">
                <img
                  :src="message.avatar || require('./default_person.png')"
                  :alt="message.name || '参会人员'"
                  class="avatar-img"
                />
              </div>
              <div class="transcript-content-wrapper">
                <div class="transcript-header">
                  <span class="transcript-name">{{ message.name || '参会人员' }}</span>
                  <span class="transcript-time">{{ message.time }}</span>
                </div>
                <div class="transcript-bubble">
                  <div class="transcript-content">
                    {{ message.content }}
                    <span v-if="message.isStreaming" class="streaming-cursor">|</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 实时转写内容 -->
            <div v-if="currentTranscript" class="transcript-item current">
              <div class="transcript-avatar">
                <img
                  :src="require('./default_person.png')"
                  alt="参会人员"
                  class="avatar-img"
                />
              </div>
              <div class="transcript-content-wrapper">
                <div class="transcript-header">
                  <span class="transcript-name">参会人员</span>
                  <span class="transcript-time">实时</span>
                </div>
                <div class="transcript-bubble">
                  <div class="transcript-content">{{ currentTranscript }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：AI助手区域 -->
      <div class="right-panel">
        <div class="panel-header">
          <h3>AI会议助手</h3>
        </div>

        <!-- 使用AI助手组件 -->
        <ai-assistant-drawer
          :visible="aiAssistantVisible"
          :meeting-minutes="meetingMinutes"
          :generating="generatingMinutes"
          :conversation-id.sync="conversationId"
          :initial-query.sync="initialAIQuery"
          @regenerate="regenerateMinutes"
          @export="exportMinutes"
        />
      </div>
    </div>



    <!-- 实时转写状态显示 -->
    <div class="transcription-status" v-if="isRecording || currentTranscript">
      <div class="status-indicator">
        <i class="el-icon-microphone" :class="{ 'recording': isRecording }"></i>
        <span v-if="isRecording">正在转写中...</span>
        <span v-else-if="currentTranscript">转写完成</span>
      </div>
      <div class="current-transcript" v-if="currentTranscript">
        {{ currentTranscript }}
      </div>
    </div>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import AiAssistantDrawer from './AiAssistantDrawer.vue'

export default {
  name: 'MeetingAgent',
  components: {
    AiAssistantDrawer
  },
  data() {
    return {
      isRecording: false,
      recognition: null,
      currentTranscript: '',
      messages: [],
      // AI助手抽屉
      aiAssistantVisible: false,
      generatingMinutes: false,
      meetingMinutes: null,
      initialAIQuery: '',
      // 对话会话ID（用于连续对话）
      conversationId: '',
      // WebSocket 转写
      ws: null,
      wsUrl: 'ws://*************:8082/transcribe-ws/ws/transcribe?lang=auto',
      useWebSocket: true,
      // 音频采集与发送
      audioContext: null,
      audioSource: null,
      audioProcessor: null,
      inputSampleRate: 44100,
      targetSampleRate: 16000,
      pcmQueue: [], // Int16Array 片段队列
      sendTimer: null
    }
  },
  computed: {
    ...mapGetters(['avatar', 'name']),
    userAvatar() {
      // 固定为默认头像
      return require('@/views/meeting/default_person.png')
    },
    userName() {
      return this.name || '我'
    },
    // 仅转写的消息
    transcriptMessages() {
      return this.messages.filter(msg => msg.type === 'transcript')
    },
    // 所有消息（包括系統消息和轉寫消息）
    allMessages() {
      return this.messages.filter(msg => msg.type === 'transcript' || msg.type === 'system')
    }
  },
  mounted() {
    this.scrollToBottom()

    // 檢查是否有傳遞的轉寫數據
    if (this.$route.params.transcriptData) {
      this.handleUploadedTranscript(this.$route.params.transcriptData, this.$route.params.fileName);
    }

    // 若使用 WebSocket 流式转写，则不启用浏览器内置识别，避免冲突
    if (!this.useWebSocket) {
    this.initSpeechRecognition()
    }
  },

  beforeDestroy() {
    // 清理语音识别
    if (this.recognition && this.isRecording) {
      this.stopRecording()
    }
  },
  methods: {
    // 初始化并开始从麦克风采集音频
    async initMicStream() {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('当前浏览器不支持麦克风访问')
      }
      const stream = await navigator.mediaDevices.getUserMedia({ audio: { echoCancellation: true, noiseSuppression: true, autoGainControl: true } })
      const AudioCtx = window.AudioContext || window.webkitAudioContext
      this.audioContext = new AudioCtx()
      try { await this.audioContext.resume() } catch (e) {}
      this.inputSampleRate = this.audioContext.sampleRate
      this.audioSource = this.audioContext.createMediaStreamSource(stream)
      const bufferSize = 4096
      this.audioProcessor = this.audioContext.createScriptProcessor(bufferSize, 1, 1)
      this.audioProcessor.onaudioprocess = (e) => {
        const input = e.inputBuffer.getChannelData(0)
        const downsampled = this.downsampleTo16k(input, this.inputSampleRate, this.targetSampleRate)
        if (downsampled && downsampled.length > 0) {
          this.pcmQueue.push(downsampled)
        }
      }
      // 通过零增益节点避免声音回放
      const silentGain = this.audioContext.createGain()
      silentGain.gain.value = 0
      this.audioSource.connect(this.audioProcessor)
      this.audioProcessor.connect(silentGain)
      silentGain.connect(this.audioContext.destination)

      // 周期发送音频块（约300ms）
      if (this.sendTimer) clearInterval(this.sendTimer)
      this.sendTimer = setInterval(() => {
        this.flushPcmQueue()
      }, 300)
    },

    // 将 Float32 转 16kHz/Int16 PCM
    downsampleTo16k(float32Array, inRate, outRate) {
      if (outRate === inRate) {
        return this.floatTo16BitPCM(float32Array)
      }
      const ratio = inRate / outRate
      const newLen = Math.floor(float32Array.length / ratio)
      const downsampled = new Float32Array(newLen)
      let i = 0
      let j = 0
      while (i < newLen) {
        const nextJ = Math.floor((i + 1) * ratio)
        // 取区间平均，降低别名
        let sum = 0
        let count = 0
        for (let k = j; k < nextJ && k < float32Array.length; k++) {
          sum += float32Array[k]
          count++
        }
        downsampled[i] = count > 0 ? sum / count : 0
        i++
        j = nextJ
      }
      return this.floatTo16BitPCM(downsampled)
    },

    floatTo16BitPCM(float32Array) {
      const len = float32Array.length
      const result = new Int16Array(len)
      for (let i = 0; i < len; i++) {
        let s = Math.max(-1, Math.min(1, float32Array[i]))
        // 转为有符号16位整数
        result[i] = s < 0 ? s * 0x8000 : s * 0x7FFF
      }
      return result
    },

    concatInt16Arrays(chunks) {
      if (!chunks || chunks.length === 0) return new Int16Array(0)
      let total = 0
      for (const c of chunks) total += c.length
      const out = new Int16Array(total)
      let offset = 0
      for (const c of chunks) {
        out.set(c, offset)
        offset += c.length
      }
      return out
    },

    flushPcmQueue() {
      if (!this.ws || this.ws.readyState !== WebSocket.OPEN) return
      if (!this.pcmQueue.length) return
      const payload = this.concatInt16Arrays(this.pcmQueue)
      this.pcmQueue = []
      try {
        this.ws.send(payload.buffer)
      } catch (e) {
        console.error('发送音频失败:', e)
      }
    },

    stopMicStream() {
      try {
        if (this.sendTimer) { clearInterval(this.sendTimer); this.sendTimer = null }
        if (this.audioProcessor) { this.audioProcessor.disconnect(); this.audioProcessor.onaudioprocess = null; this.audioProcessor = null }
        if (this.audioSource) { this.audioSource.disconnect(); this.audioSource = null }
        if (this.audioContext) { try { this.audioContext.close() } catch (e) {} this.audioContext = null }
      } catch (e) {
        console.error('停止音频采集失败:', e)
      }
    },
    // 连接 WebSocket
    connectWebSocket() {
      if (this.ws) return
      try {
        this.ws = new WebSocket(this.wsUrl)

        this.ws.onopen = async () => {
          try { this.ws.binaryType = 'arraybuffer' } catch (e) {}
          this.msgSuccess('已连接转写服务')
          // 连接建立后开始采集并发送音频
          try {
            await this.initMicStream()
          } catch (e) {
            console.error('麦克风初始化失败:', e)
            this.msgError('麦克风权限或初始化失败')
          }
        }

        this.ws.onmessage = (event) => {
          // 调试日志，便于排查未显示问题
          try { console.debug('[WS message]', event.data) } catch (e) {}
          const handleText = (raw) => {
            try {
              const dataStr = raw
              let parsed = null
              try {
                parsed = JSON.parse(dataStr)
              } catch (e) {
                // 非JSON则直接作为最终文本
                if (dataStr && dataStr.trim()) {
                  this.addTranscriptMessage(dataStr)
                }
                return
              }

              if (!parsed || typeof parsed !== 'object') return

              let codeNum = Number(parsed.code)
              if (Number.isNaN(codeNum) && parsed.code === 0) codeNum = 0
              const info = parsed.info
              const payload = parsed.data === undefined ? {} : parsed.data

              if (codeNum !== 0) {
                // 错误，但忽略 detect speech 这类状态提示
                const infoStr = (info == null ? '' : String(info))
                const infoLower = infoStr.toLowerCase()
                if (infoLower !== 'detect speech') {
                  this.msgError(infoStr || '转写服务错误')
                }
                return
              }

              // 兼容多种返回：data 为字符串/对象；info 也可能是 JSON 字符串包含 text
              let text = ''
              let isFinal = false
              if (typeof payload === 'string') {
                text = String(payload)
                isFinal = true // data 直接为文本时，当作最终结果
              } else {
                text = (payload.text == null ? '' : String(payload.text))
                isFinal = !!payload.is_final
              }

              if (!text && typeof info === 'string') {
                // info 可能是 JSON 字符串，提取其中的 text
                try {
                  const infoObj = JSON.parse(info)
                  if (infoObj && typeof infoObj.text === 'string') {
                    text = infoObj.text
                  }
                } catch (e) {
                  // ignore
                }
              }

              // 去除模型标记，如 <|zh|><|NEUTRAL|> 等
              if (text) {
                text = text.replace(/<\|[^|]+\|>/g, '')
              }

              text = (text || '').trim()
              if (!text) return

              // 服务端状态提示，如 detect speech，不作为错误提示
              if (typeof info === 'string' && info.toLowerCase() === 'detect speech') {
                if (!isFinal) {
                  this.currentTranscript = text
                  this.scrollTranscriptBottom()
                  return
                }
              }

              if (isFinal) {
                // 最终结果：写入历史并清空临时
                this.addTranscriptMessage(text)
                this.currentTranscript = ''
              } else {
                // 中间结果：只更新实时面板
                this.currentTranscript = text
                this.scrollTranscriptBottom()
              }
            } catch (error) {
              console.error('处理消息失败:', error)
            }
          }

          if (typeof event.data === 'string') {
            handleText(event.data)
          } else if (event.data && typeof event.data.text === 'function') {
            // Blob 或其他可转文本类型
            event.data.text().then(handleText).catch((err) => {
              console.error('读取消息失败:', err)
            })
          } else if (event.data instanceof ArrayBuffer) {
            // 处理二进制帧
            try {
              const text = new TextDecoder('utf-8').decode(new Uint8Array(event.data))
              handleText(text)
            } catch (e) {
              console.error('二进制消息解码失败:', e)
            }
          }
        }

        this.ws.onerror = (err) => {
          try { console.error('WebSocket 错误:', err) } catch (e) {}
          this.msgError('转写服务连接错误')
        }

        this.ws.onclose = (evt) => {
          this.ws = null
          try { this._wsPingTimer && clearInterval(this._wsPingTimer); this._wsPingTimer = null } catch (e) {}
          try { console.warn('[WS closed]', evt && (evt.code + ' ' + (evt.reason || ''))) } catch (e) {}
          // 停止音频采集
          this.stopMicStream()
          if (this.isRecording) {
            // 如果录音标记仍为真，说明是异常断开
        this.isRecording = false
            this.msgInfo('转写服务已断开')
          }
        }
      } catch (error) {
        console.error('连接转写服务失败:', error)
        this.msgError('连接转写服务失败')
      }
    },

    // 关闭 WebSocket
    closeWebSocket() {
      try {
        if (this.ws) {
          try { this.ws.close(1000, 'client_stop') } catch (e) { this.ws.close() }
          this.ws = null
          try { this._wsPingTimer && clearInterval(this._wsPingTimer); this._wsPingTimer = null } catch (e) {}
        }
      } catch (error) {
        console.error('关闭 WebSocket 失败:', error)
      }
    },

    // 初始化语音识别
    initSpeechRecognition() {
      if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
        this.recognition = new SpeechRecognition()

        // 配置语音识别
        this.recognition.continuous = true
        this.recognition.interimResults = true
        this.recognition.lang = 'zh-CN'

        // 监听识别结果
        this.recognition.onresult = (event) => {
          let finalTranscript = ''
          let interimTranscript = ''

          for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript
            if (event.results[i].isFinal) {
              finalTranscript += transcript
            } else {
              interimTranscript += transcript
            }
          }

          // 更新当前转写内容
          this.currentTranscript = interimTranscript

          // 如果有最终结果，添加到消息列表
          if (finalTranscript) {
            this.addTranscriptMessage(finalTranscript)
            this.currentTranscript = ''
          }
        }

        // 监听错误
        this.recognition.onerror = (event) => {
          console.error('语音识别错误:', event.error)
          this.msgError(`语音识别错误: ${event.error}`)
          this.isRecording = false
        }

        // 监听结束
        this.recognition.onend = () => {
          if (this.isRecording) {
            // 如果还在录音状态，重新开始识别
              this.recognition.start()
          }
        }
      } else {
        this.msgWarning('您的浏览器不支持语音识别功能')
      }
    },

    // 切换录音状态
    toggleRecording() {
      if (!this.recognition) {
        // 如果未初始化浏览器语音识别，但我们使用 WebSocket，则允许继续
        if (!this.useWebSocket) {
          this.msgError('语音识别未初始化')
          return
        }
      }

      if (this.isRecording) {
        this.stopRecording()
      } else {
        this.startRecording()
      }
    },

    // 开始录音
    startRecording() {
      try {
        this.isRecording = true
        this.currentTranscript = ''
        // 优先使用 WebSocket 方式
        if (this.useWebSocket) {
          this.connectWebSocket()
        } else if (this.recognition) {
          this.recognition.start()
        }
        this.msgSuccess('开始语音转写')
      } catch (error) {
        console.error('开始录音失败:', error)
        this.msgError('开始录音失败')
        this.isRecording = false
      }
    },

    // 停止录音
    stopRecording() {
      try {
        this.isRecording = false
        // 停止前将临时转写落盘
        this.finalizeCurrentTranscript()

        if (this.useWebSocket) {
          // 给服务端一个"结束"信号（若协议支持），并等待短暂时间接收最后数据
          try {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
              try { this.ws.send(JSON.stringify({ action: 'stop' })) } catch (e) {}
            }
          } catch (e) {}

          const closeLater = () => this.closeWebSocket()
          // 等待 300ms 以接收末尾数据
          setTimeout(closeLater, 300)
        }
        if (this.recognition) {
          try { this.recognition.stop() } catch (e) {}
        }
        // 停止音频采集
        this.stopMicStream()
        this.msgInfo('停止语音转写')
      } catch (error) {
        console.error('停止录音失败:', error)
      }
    },

    // 添加转写消息到聊天列表
    addTranscriptMessage(transcript) {
      if (transcript.trim()) {
        const message = {
          type: 'transcript',
          content: transcript.trim(),
          time: this.getCurrentTime()
        }
        this.messages.push(message)
        this.scrollToBottom()
        this.scrollTranscriptBottom()
      }
    },

    // 處理上傳的轉寫數據
    handleUploadedTranscript(transcriptData, fileName) {
      // 添加文件信息消息
      this.messages.push({
        type: 'system',
        content: `已上传音频文件：${fileName}`,
        time: this.getCurrentTime()
      });

      // 處理轉寫數據
      if (transcriptData) {
        let textContent = '';

        // 如果返回的是字符串，直接使用
        if (typeof transcriptData === 'string') {
          textContent = transcriptData;
        }
        // 如果返回的是對象，優先提取 data 字段
        else if (typeof transcriptData === 'object') {
          // 優先提取 data 字段
          if (transcriptData.data) {
            textContent = transcriptData.data;
          }
          // 如果沒有 data 字段，嘗試其他可能的字段名
          else {
            textContent = transcriptData.text || transcriptData.content || transcriptData.transcript || transcriptData.result;
          }

          // 如果仍然沒有找到文本內容，顯示整個響應（用於調試）
          if (!textContent) {
            console.log('未找到文本內容，完整響應:', transcriptData);
            textContent = JSON.stringify(transcriptData, null, 2);
          }
        }

        // 按句號分隔並逐句添加
        if (textContent) {
          this.addTranscriptSentences(textContent);
        }
      }

      this.scrollToBottom();
      this.scrollTranscriptBottom();
    },

    // 按句號分隔並逐句添加轉寫內容
    addTranscriptSentences(text) {
      if (!text || !text.trim()) return;

      // 按句號、問號、感嘆號分隔句子
      const sentences = text.split(/[。！？.!?]+/).filter(sentence => sentence.trim());

      if (sentences.length === 0) {
        // 如果沒有找到句子分隔符，使用流式輸出整個文本
        this.streamText(text);
        return;
      }

      // 逐句流式輸出
      let currentIndex = 0;
      const streamNextSentence = () => {
        if (currentIndex < sentences.length) {
          const sentence = sentences[currentIndex].trim();
          if (sentence) {
            this.streamText(sentence);
            currentIndex++;
            setTimeout(streamNextSentence, 1000); // 每句間隔1秒
          }
        }
      };

      streamNextSentence();
    },

    // 流式輸出文本
    streamText(text) {
      if (!text || !text.trim()) return;

      // 創建一個新的消息對象
      const message = {
        type: 'transcript',
        content: '',
        time: this.getCurrentTime(),
        isStreaming: true
      };

      // 添加到消息列表
      this.messages.push(message);
      this.scrollToBottom();
      this.scrollTranscriptBottom();

      // 逐字輸出
      let currentIndex = 0;
      const streamInterval = setInterval(() => {
        if (currentIndex < text.length) {
          message.content += text[currentIndex];
          currentIndex++;

          // 滾動到底部
          this.$nextTick(() => {
            this.scrollToBottom();
            this.scrollTranscriptBottom();
          });
        } else {
          // 流式輸出完成
          clearInterval(streamInterval);
          message.isStreaming = false;
        }
      }, 50); // 每50毫秒輸出一個字符
    },

    // 将当前临时文本写入最终列表
    finalizeCurrentTranscript() {
      const text = (this.currentTranscript || '').trim()
      if (text) {
        this.addTranscriptMessage(text)
        this.currentTranscript = ''
      }
    },





    // 显示AI助手抽屉
    showMeetingSummary() {
      // 检查是否有会议内容
      const transcriptMessages = this.messages.filter(msg =>
        msg.type === 'transcript' || msg.type === 'user'
      )
      if (transcriptMessages.length === 0) {
        this.msgWarning('暂无会议内容可总结')
        return
      }

      // 重置对话状态，确保每次都是新的对话
      this.conversationId = ''

      // 准备会议转写文本
      const transcriptText = transcriptMessages.map(m => `${m.time} ${m.content}`).join('\n')

      // 设置初始查询内容
      this.initialAIQuery = `请帮我总结会议纪要，分点输出。\n会议转写记录如下（含时间戳）：\n${transcriptText}`

      // 打开抽屉
      this.aiAssistantVisible = true
    },

    // 生成会议纪要
    async generateMeetingMinutes() {
      this.generatingMinutes = true
      try {
        // 提取所有转写与用户内容
        const transcriptMessages = this.messages.filter(msg =>
          msg.type === 'transcript' || msg.type === 'user'
        )

        if (transcriptMessages.length === 0) {
          this.msgWarning('暂无会议内容可总结')
          this.generatingMinutes = false
          return
        }

        // 现在通过流式输出处理，不再需要这里生成
        // 流式输出会在 AiAssistantDrawer 中处理
      } catch (error) {
        console.error('生成会议纪要失败:', error)
        this.msgError('生成会议纪要失败')
      } finally {
        this.generatingMinutes = false
      }
    },

    // 模拟AI生成过程
    simulateAIGeneration() {
      return new Promise(resolve => {
        setTimeout(resolve, 2000) // 模拟2秒生成时间
      })
    },

    // 生成纪要内容
    generateMinutesContent(messages) {
      const content = messages.map(msg => msg.content).join(' ')

      // 简单的关键词提取和总结（实际应该调用AI接口）
      const keyWords = ['项目', '计划', '问题', '解决', '决定', '安排', '时间', '负责']
      const foundKeyWords = keyWords.filter(word => content.includes(word))

      return {
        summary: `本次会议共记录了${messages.length}条发言内容，主要围绕${foundKeyWords.slice(0, 3).join('、')}等话题进行讨论。会议内容已通过AI智能分析生成以下纪要。`,
        keyPoints: [
          '参会人员积极发言，讨论热烈',
          '针对关键问题达成了初步共识',
          '明确了下一步的工作方向',
          '确定了相关责任人和时间节点'
        ],
        decisions: [
          '确认项目整体进度符合预期',
          '决定加强团队协作和沟通',
          '同意采用新的工作流程'
        ],
        actionItems: [
          '整理会议记录并分发给相关人员',
          '制定详细的执行计划',
          '安排下次会议时间'
        ]
      }
    },

    // 重新生成纪要
    regenerateMinutes() {
      this.generateMeetingMinutes()
    },

    // 导出纪要
    exportMinutes() {
      if (!this.meetingMinutes) {
        this.msgWarning('暂无纪要内容可导出')
        return
      }

      // 模拟导出功能
      this.msgSuccess('会议纪要导出成功')
    },

    // 发送聊天到AI（Dify兼容）
    async sendChatToAI(query, conversationId = '') {
      try {
        // 使用开发代理以避免浏览器跨域
        const url = '/ai-api/v1/chat-messages'
        const token = 'app-4So8okV2nKPILIMG3vDTYdQi'
        const body = {
          inputs: {},
          query,
          response_mode: 'blocking', // 使用阻塞模式方便一次性拿到结果
          conversation_id: conversationId || '',
          user: 'web-user'
        }
        const resp = await fetch(url, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(body)
        })
        if (!resp.ok) {
          throw new Error(`HTTP ${resp.status}`)
        }
        const data = await resp.json().catch(async () => {
          // 若是文本返回
          const txt = await resp.text()
          try { return JSON.parse(txt) } catch (_) { return { answer: txt } }
        })
        return data
      } catch (e) {
        console.error('调用AI接口失败:', e)
        this.msgError('AI服务调用失败')
        return null
      }
    },



    getCurrentTime() {
      const now = new Date()
      const hours = String(now.getHours()).padStart(2, '0')
      const minutes = String(now.getMinutes()).padStart(2, '0')
      const seconds = String(now.getSeconds()).padStart(2, '0')
      return `${hours}:${minutes}:${seconds}`
    },

    scrollToBottom() {
      this.$nextTick(() => {
        const container = this.$refs.chatContainer
        if (container) {
          container.scrollTop = container.scrollHeight
        }
      })
    },

    scrollTranscriptBottom() {
      this.$nextTick(() => {
        const panel = this.$refs.transcriptPanel
        if (panel) {
          panel.scrollTop = panel.scrollHeight
        }
      })
    },

    // 消息提示方法
    msgSuccess(message) {
      this.$message.success(message)
    },
    msgError(message) {
      this.$message.error(message)
    },
    msgWarning(message) {
      this.$message.warning(message)
    },
    msgInfo(message) {
      this.$message.info(message)
    }
  }
}
</script>

<style lang="scss" scoped>
.meeting-agent-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.main-content {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 16px;
  overflow: hidden;
}

.left-panel {
  flex: 6;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.right-panel {
  flex: 4;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
  background: #fff;

  h3 {
    margin: 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
  }

  .status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #909399;

    &.recording {
      color: #f56c6c;
      animation: pulse 1.5s infinite;
    }

    i {
      font-size: 14px;
    }
  }

  .ai-actions {
    display: flex;
    gap: 8px;
  }
}

.transcript-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px;
}

.empty-transcript {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;

  i {
    font-size: 48px;
    margin-bottom: 16px;
    color: #dcdfe6;
  }

  p {
    margin: 4px 0;
    font-size: 14px;

    &.tip {
      font-size: 12px;
      color: #c0c4cc;
    }
  }
}

.streaming-cursor {
  color: #e6a23c;
  font-weight: bold;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.transcript-messages {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

  .transcript-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 16px;
    position: relative;

  .transcript-avatar {
    flex-shrink: 0;
    margin-top: 8px;
    display: flex;
    align-items: center;

    .avatar-img {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      object-fit: cover;
    }
  }

  .transcript-content-wrapper {
    flex: 1;
    min-width: 0;
  }

  .transcript-bubble {
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #409eff;
    padding: 12px;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: -8px;
      top: 6px;
      width: 0;
      height: 0;
      border-top: 6px solid transparent;
      border-bottom: 6px solid transparent;
      border-right: 8px solid #409eff;
    }
  }

  &.current {
    .transcript-bubble {
      border-left-color: #67c23a;
      background: #f0f9ff;

      &::before {
        border-right-color: #67c23a;
      }
    }
  }

  &.system-message {
    .transcript-bubble {
      border-left-color: #67c23a;
      background: #f0f9ff;

      &::before {
        border-right-color: #67c23a;
      }
    }
  }

  &.streaming-message {
    .transcript-bubble {
      border-left-color: #e6a23c;
      background: #fdf6ec;

      &::before {
        border-right-color: #e6a23c;
      }
    }
  }

  .transcript-header {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
    margin-bottom: 4px;
    padding: 0 4px;
  }

  .transcript-name {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
  }

  .transcript-time {
    font-size: 12px;
    color: #909399;
  }

  .transcript-content {
    color: #606266;
    line-height: 1.5;
    font-size: 14px;
  }
}

.page-header {
  background: #fff;
  padding: 16px 20px;

  .page-title {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;

    i {
      font-size: 24px;
      color: #409eff;
    }

    h1 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #303133;
    }
  }

  .page-description {
    font-size: 14px;
    color: #909399;
    margin-left: 36px;
  }
}

.meeting-controls {
  padding: 16px 20px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: center;
  gap: 16px;

  .action-btn {
    color: #409eff;

    transition: all 0.3s ease;
    padding: 8px 16px;
    border: 1px solid #409eff;
    border-radius: 4px;

    i {
      margin-right: 6px;
    }

    &:hover {
      background-color: #409eff;
      color: #fff;
    }

    &.recording {
      color: #f56c6c;
      border-color: #f56c6c;
      animation: pulse 1.5s infinite;

      &:hover {
        background-color: #f56c6c;
        color: #fff;
      }
    }
  }

}

.transcript-panel {
  margin: 12px 16px 0 16px;
  background: #fff;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  .panel-header {
    display: flex;
    align-items: center;
    padding: 10px 12px;
    border-bottom: 1px solid #ebeef5;
    font-size: 14px;
    color: #303133;

    i { margin-right: 6px; color: #409eff; }
    .status-tag {
      margin-left: auto;
    font-size: 12px;
    color: #909399;
      &.active { color: #67c23a; }
    }
  }

  .panel-body {
    max-height: 180px;
    overflow-y: auto;
    padding: 10px 12px;

    .empty-tip {
      color: #909399;
      font-size: 13px;
      text-align: center;
      padding: 12px 0;
    }

    .ts-item {
      display: flex;
      gap: 8px;
      align-items: flex-start;
      padding: 6px 0;

      &.interim .ts-text { color: #409eff; }

      .ts-time {
        flex-shrink: 0;
        width: 44px;
        color: #c0c4cc;
        font-size: 12px;
      }

      .ts-text {
        color: #606266;
        line-height: 1.5;
        word-break: break-word;
        flex: 1;
      }
    }
  }
}

.chat-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;

  .chat-messages {
    max-width: 800px;
    margin: 0 auto;
  }

  .message-item {
    display: flex;
    margin-bottom: 16px;

    &.user {
      flex-direction: row-reverse;

      .message-content {
        margin-right: 12px;
        margin-left: 0;

        .message-text {
          background: #409eff;
          color: #fff;
        }
      }
    }

    &.ai {
      .message-content {
        margin-left: 12px;
      }
    }

    &.transcript {
      .message-content {
        margin-left: 12px;
      }
    }
  }

  .message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .ai-avatar {
      width: 40px;
      height: 40px;
      background: #67c23a;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      font-size: 20px;
    }

    .transcript-avatar {
      width: 40px;
      height: 40px;
      background: #409eff;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      font-size: 18px;
    }
    .ws-avatar {
      width: 40px;
      height: 40px;
      background: #e6a23c;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      font-size: 18px;
    }
  }

  .message-content {
    max-width: 60%;

    .message-meta {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 4px;
      .message-name {
        font-size: 12px;
        color: #606266;
        font-weight: 600;
      }
    .message-time {
      font-size: 12px;
      color: #c0c4cc;
      }
    }

    .message-text {
      background: #fff;
      padding: 12px 16px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      word-wrap: break-word;
      line-height: 1.5;

      &.transcript-text {
        background: #f0f9ff;
        border-left: 4px solid #409eff;

        .transcript-label {
          color: #409eff;
          font-size: 12px;
          font-weight: 600;
          margin-right: 8px;
        }
      }
    }
  }
}



// 转写状态样式
.transcription-status {
  position: fixed;
  bottom: 200px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 12px 20px;
  border-radius: 20px;
  z-index: 1000;
  max-width: 80%;

  .status-indicator {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    i {
      margin-right: 8px;
      font-size: 16px;

      &.recording {
        color: #f56c6c;
        animation: pulse 1.5s infinite;
      }
    }
  }

  .current-transcript {
    font-size: 14px;
    line-height: 1.4;
    max-height: 60px;
    overflow-y: auto;
  }
}

// 会议纪要弹窗样式
.minutes-content {
  min-height: 300px;

  .generated-minutes {
    .minutes-section {
      margin-bottom: 24px;

      h3 {
        color: #303133;
        font-size: 16px;
        margin-bottom: 12px;
        border-bottom: 2px solid #409eff;
        padding-bottom: 8px;
      }

      p {
        color: #606266;
        line-height: 1.6;
        margin-bottom: 12px;
      }

      ul {
        margin: 0;
        padding-left: 20px;

        li {
          color: #606266;
          line-height: 1.6;
          margin-bottom: 8px;
        }
      }
    }
  }
}

// 系统消息样式
.system-message {
  .transcript-content {
    background: #f0f9ff;
    border-left: 4px solid #67c23a;
    color: #67c23a;
    font-style: italic;
  }
}

// 动画效果
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
</style>
