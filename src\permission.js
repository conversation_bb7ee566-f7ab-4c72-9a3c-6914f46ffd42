import router from "./router";
import store from "./store";
import { Message } from "element-ui";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import { getToken } from "@/utils/auth";
import { checkTicketInUrl } from "@/utils/sso";
import defaultSettings from "@/settings";

NProgress.configure({ showSpinner: false });

const whiteList = ["/login", "/auth-redirect", "/bind", "/register"];

router.beforeEach(async (to, from, next) => {
  NProgress.start();

  // 检查URL中是否有ticket参数（单点登录）
  const ticketInfo = checkTicketInUrl();
  if (ticketInfo && !getToken()) {
    try {
      // 使用store的SSOLogin action进行验证
      await store.dispatch("SSOLogin", {
        ticket: ticketInfo.ticket,
        service: ticketInfo.service,
      });
      // 验证成功后，重新检查token并继续路由逻辑
    } catch (error) {
      console.error("ST验证失败:", error);
      Message.error("单点登录验证失败");
      // ST验证失败，继续正常的路由逻辑
    }
  }

  if (getToken()) {
    /* has token*/
    if (to.path === "/login") {
      next({ path: "/" });
      NProgress.done();
    } else {
      if (store.getters.roles.length === 0) {
        // 判断当前用户是否已拉取完user_info信息
        store
          .dispatch("GetInfo")
          .then((res) => {
            // 拉取user_info
            const roles = res.roles;
            store.dispatch("GenerateRoutes", { roles }).then((accessRoutes) => {
              // 根据roles权限生成可访问的路由表
              router.addRoutes(accessRoutes); // 动态添加可访问路由表
              next({ ...to, replace: true }); // hack方法 确保addRoutes已完成
            });
          })
          .catch((err) => {
            store.dispatch("LogOut").then(() => {
              Message.error(err);
              next({ path: "/" });
            });
          });
      } else {
        next();
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next();
    } else {
      next(`/login?redirect=${to.fullPath}`); // 否则全部重定向到登录页
      NProgress.done();
    }
  }
});

router.afterEach((to) => {
  NProgress.done();

  // 设置页面标题
  const title = to.meta?.title;
  if (title) {
    document.title = title;
  } else {
    // 如果没有设置title，使用默认标题
    document.title = defaultSettings.title;
  }
});
