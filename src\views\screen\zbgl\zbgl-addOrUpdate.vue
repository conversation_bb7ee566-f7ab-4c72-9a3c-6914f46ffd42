<style>
.sql-formatter-button {
  float: right;
  margin-top: 6px;
  padding: 8px 12px;
  font-size: 12px;
}
.el-checkbox {
  margin-right: 0 !important;
  cursor: auto !important;
}
.checkbox > .el-checkbox__input {
  display: none !important;
}
.el-tag {
  font-size: 14px !important;
}
</style>
<template>
  <div>
    <el-dialog
      :title="dataForm.type == 'add' ? '新增' : '修改'"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :visible.sync="visible"
      width="60%"
    >
      <el-form
        ref="dataForm"
        :model="dataForm"
        :rules="dataRule"
        :disabled="formDisabled"
        label-width="110px"
        @keyup.enter.native=""
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="指标编码" prop="bm">
              <el-input v-model="dataForm.bm" :readonly="bmReadonly"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属页面" prop="mch1Path">
              <!-- <el-select v-model="dataForm.mch1" filterable placeholder="请选择指标领域（专题页面）" style="width: 100%">
                <el-option v-for="item in this.ymzts" :label="item.dataName" :value="item.dataCode"></el-option>
              </el-select> -->
              <el-cascader
                v-model="dataForm.mch1Path"
                :show-all-levels="true"
                placeholder="所属页面"
                :options="ymzts"
                :props="{
                  children: 'children',
                  label: 'dataName',
                  value: 'id',
                  checkStrictly: true,
                  multiple: false,
                }"
                clearable
                filterable
                style="width: 100%"
              />
              <!-- style="width: 440px;" -->
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="指标概述" prop="mch2">
              <el-input v-model="dataForm.mch2"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="每秒限流" prop="xlNum">
              <el-input v-model="dataForm.xlNum"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据来源" prop="sjly">
              <el-select
                v-model="dataForm.sjly"
                placeholder="数据来源 "
                style="width: 100%"
                @change="sjlyOnChange('')"
              >
                <el-option
                  v-for="item in sjlys"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="展示方式" prop="mch3">
              <el-tooltip
                class="item"
                effect="dark"
                content="分页默认返回10条数据"
                placement="bottom"
              >
                <el-select
                  v-model="dataForm.mch3"
                  placeholder="选择展示方式 "
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in stypes"
                    :key="item.dictValue"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                  ></el-option>
                </el-select>
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据状态" prop="dataStatus">
              <el-select
                v-model="dataForm.dataStatus"
                placeholder="选择数据状态 "
                style="width: 100%"
              >
                <el-option
                  v-for="item in statuList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请求方式" prop="qqfs">
              <el-select
                v-model="dataForm.qqfs"
                placeholder="请求方式 "
                style="width: 100%"
              >
                <el-option
                  v-for="item in qqfss"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="返回格式" prop="mch4">
              <el-select
                v-model="dataForm.mch4"
                placeholder="选择返回格式 "
                style="width: 100%"
              >
                <el-option
                  v-for="item in types"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="指标类" prop="zbl">
              <treeselect v-model="dataForm.zbl" :options="indexOptions" :normalizer="normalizer" placeholder="请选择指标类" style="width:100%"/>
            </el-form-item>
          </el-col> -->
          <el-col :span="24">
            <el-form-item label="指标项" prop="zbx">
              <el-button type="primary" size="mini" @click="showZbx">
                选择指标
              </el-button>
              <el-checkbox-group v-model="dataForm.zbx">
                <!-- <el-checkbox
                  v-for="dict in zbxOptions"
                  :value="dict.indexId"
                  :label="dict.indexId"
                  @change="selectBox(dict.indexId,dict.indexName)">
                  {{dict.indexName}}
                </el-checkbox> -->
                <el-checkbox
                  v-for="dict in zbxOptions"
                  :key="dict.id"
                  class="checkbox"
                  :value="dict.id"
                  :label="dict.id"
                >
                  <el-tag type="success" size="medium">
                    {{ dict.level3 }}
                  </el-tag>
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="状态信息" prop="status" >
              <el-tooltip class="item" effect="dark"  placement="bottom">
                <el-select v-model="dataForm.status" placeholder="选择状态是否正常 " style="width:100%">
                  <el-option v-for="item in status" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-tooltip>
            </el-form-item>
          </el-col> -->

          <el-col :span="90">
            <el-form-item
              v-show="jksj_judge_show && dataForm.qqfs === '2'"
              label="是否是json"
              prop="isjson"
            >
              <el-select
                v-model="dataForm.isjson"
                placeholder="传参是否是json数据 "
                style="width: 300px"
                @change="$forceUpdate()"
              >
                <el-option
                  v-for="item in stateArr"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item v-show="jkdz_ele_show" label="接口地址" prop="jkdz">
              <el-input
                v-model="dataForm.jkdz"
                type="textarea"
                placeholder="请以http,https开头"
                :autosize="{ minRows: 1, maxRows: 3 }"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              v-show="mmkj_ele_show"
              label="命名空间"
              prop="mmkj"
              :rules="dataRule.mmkj"
            >
              <el-input v-model="dataForm.mmkj"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              v-show="ffm_ele_show"
              label="方法名"
              prop="ffm"
              :rules="dataRule.ffm"
            >
              <el-input v-model="dataForm.ffm"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item>
          <div
            v-show="dataForm.isjson == '1' && jksj_judge_show"
            class="text-area"
          >
            <el-input
              v-model="dataForm.jsondata"
              type="textarea"
              placeholder="传入json参数"
              :autosize="{ minRows: 3, maxRows: 10 }"
            ></el-input>
            <!-- <textarea placeholder="传入json参数" style="width:900px;">
             </textarea> -->
          </div>
        </el-form-item>
        <el-form-item label="指标参数" prop="zbcs" @change="jsonOnChange('')">
          <div v-for="(item, index) in dataForm.zbcs">
            <div>
              <el-row>
                <el-col :span="4">
                  <el-form-item
                    :prop="'zbcs.' + index + '.csm'"
                    :rules="dataRule.csm"
                  >
                    <el-input
                      v-model="item.csm"
                      size="small"
                      style="width: 120px"
                      placeholder="参数名"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item :prop="'zbcs.' + index + '.csz'">
                    <el-input
                      v-model="item.csz"
                      size="small"
                      style="width: 120px"
                      placeholder="参数值"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="9">
                  <el-form-item :prop="'zbcs.' + index + '.csms'">
                    <el-input
                      v-model="item.csms"
                      size="small"
                      style="width: 240px"
                      placeholder="参数描述"
                    ></el-input>
                  </el-form-item>
                </el-col>

                <el-col :span="3">
                  <el-form-item label="" :prop="'zbcs.' + index + '.sfbt'">
                    <el-checkbox-group v-model="item.sfbt" size="mini">
                      <el-checkbox :label="1">参数必传</el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                </el-col>
                <el-col :span="1">
                  <el-form-item>
                    <i
                      v-if="index > 0"
                      class="el-icon-remove-outline"
                      @click="deleteCs(item)"
                    ></i>
                    <i
                      v-if="index == 0"
                      class="el-icon-circle-plus-outline"
                      @click="addCs"
                    ></i>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-form-item>
        <el-form-item v-show="key_ele_show" label="请求头参数" prop="hdcs">
          <div v-for="(item, index) in dataForm.hdcs">
            <el-row>
              <el-col :span="8">
                <el-form-item
                  :prop="'hdcs.' + index + '.keylabel'"
                  :rules="dataRule.keylabel"
                >
                  <el-input
                    v-model="item.keylabel"
                    size="small"
                    style="width: 88%"
                    placeholder="参数名"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :prop="'hdcs.' + index + '.keyval'">
                  <el-input
                    v-model="item.keyval"
                    size="small"
                    style="width: 88%"
                    placeholder="参数值"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="1">
                <el-form-item>
                  <i
                    v-if="index > 0"
                    class="el-icon-remove-outline"
                    @click="deleteHeaderCs(item)"
                  ></i>
                  <i
                    v-if="index == 0"
                    class="el-icon-circle-plus-outline"
                    @click="addHeaderCs"
                  ></i>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form-item>
        <el-form-item v-show="cdzd_ele_show" label="状态码字段名">
          <el-col :span="13">
            <el-form-item prop="cdzd" :rules="dataRule.cdzd">
              <el-input
                v-model="dataForm.cdzd"
                style="width: 95%"
                placeholder="状态码字段名,请根据接口文档返回参数填写(如：code、responseCode)"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item prop="cdz" :rules="dataRule.cdz">
              <el-input
                v-model="dataForm.cdz"
                placeholder="正常返回的状态码,请根据接口文档返回参数填写(如：200、true)"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item v-show="msgzd_ele_show" label="信息字段名" prop="msgzd">
          <el-input
            v-model="dataForm.msgzd"
            placeholder="请根据对方提供的接口文档返回参数填写。（如：message、msg）"
          ></el-input>
        </el-form-item>
        <el-form-item v-show="sjzd_ele_show" label="数据字段名" prop="sjzd">
          <el-input
            v-model="dataForm.sjzd"
            placeholder="请根据对方提供的接口文档返回参数填写。若：结构为多层嵌套,从外层开始依次填写数据字段（如：result.data[0] 请用英文'.'句号隔开）"
          ></el-input>
        </el-form-item>
        <el-form-item v-show="cssc_ele_show" label="超时限制(ms)" prop="cssc">
          <el-input v-model="dataForm.cssc"></el-input>
        </el-form-item>
        <el-form-item v-show="sql_ele_show" label="完全公开SQL" prop="sqla">
          <el-tooltip
            class="item"
            effect="dark"
            content="请按照mybatis书写规范填写Mysql查询语句，如：select a from 库名.表名"
            placement="top"
          >
            <el-input
              v-model="dataForm.sqla"
              type="textarea"
              placeholder="请按照mybatis书写规范填写Mysql查询语句，如：select a from 库名.表名"
              :autosize="{ minRows: 2, maxRows: 8 }"
            ></el-input>
          </el-tooltip>
          <el-tooltip
            class="item"
            effect="dark"
            content="不建议格式化带有标签的sql语句"
            placement="bottom"
          >
            <el-button
              type="primary"
              class="sql-formatter-button"
              @click="sqlFormatter(dataForm.sqla, 1)"
            >
              美化SQL
            </el-button>
          </el-tooltip>
        </el-form-item>
        <el-form-item v-show="false" label="半公开SQL" prop="sqlb">
          <el-input
            v-model="dataForm.sqlb"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 8 }"
          ></el-input>
          <el-tooltip
            class="item"
            effect="dark"
            content="不建议格式化带有标签的sql语句"
            placement="bottom"
          >
            <el-button
              type="primary"
              class="sql-formatter-button"
              @click="sqlFormatter(dataForm.sqlb, 2)"
            >
              美化SQL
            </el-button>
          </el-tooltip>
        </el-form-item>
        <el-form-item v-show="false" label="不公开SQL" prop="sqlc">
          <el-input
            v-model="dataForm.sqlc"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 8 }"
          ></el-input>
          <el-tooltip
            class="item"
            effect="dark"
            content="不建议格式化带有标签的sql语句"
            placement="bottom"
          >
            <el-button
              type="primary"
              class="sql-formatter-button"
              @click="sqlFormatter(dataForm.sqlc, 3)"
            >
              美化SQL
            </el-button>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="录入说明" prop="lrsm">
          <el-input
            v-model="dataForm.lrsm"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 8 }"
          ></el-input>
        </el-form-item>
        <el-form-item v-show="xgsmIsShow" label="修改说明" prop="xgsm">
          <el-input
            v-model="dataForm.xgsm"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 8 }"
          ></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="bz">
          <el-input
            v-model="dataForm.bz"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 8 }"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button
          v-show="debug_but_ele_show"
          type="primary"
          style="background-color: #07d607; border-color: #07d607"
          @click="debugScript()"
        >
          调试
        </el-button>
        <el-button @click="visible = false">取消</el-button>
        <el-button
          v-hasPermi="['screen:zbgl:add']"
          type="primary"
          @click="validateSame()"
        >
          保存
        </el-button>
      </span>
    </el-dialog>
    <!-- 弹窗-->
    <zb-debug v-if="zbDebugVisible" ref="zbDebug"></zb-debug>
    <zbx-dialog
      v-if="zbxDialogVisible"
      ref="zbxDialog"
      :zbx="zbxOptions"
      @zbxList="getZbxList"
    ></zbx-dialog>
  </div>
</template>

<script>
import { listIndex, getIndexItemList, listAllIndex } from '@/api/screen/index'
import {
  addZb,
  updateZb,
  getDpztList,
  getZbData,
  isSameBm,
  debugScript,
  getZbx,
} from '@/api/screen/zbgl'
import zbglDebug from './zbgl-debug.vue'
import zbxDialog from './zbgl-zbxDialog.vue'
import { isSpecial, isIncludeCh, isURL } from '../../../utils/validate.js'
import * as sqlFormatter from 'sql-formatter'
import { Loading } from 'element-ui'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { listToTree } from '@/utils/Tree'
import { listTreeCategory } from '@/api/screen/category'
import { handleTree } from '@/utils/ruoyi'

export default {
  components: {
    zbDebug: zbglDebug,
    zbxDialog: zbxDialog,
    Treeselect,
  },
  data() {
    //      var validateUrl = (rule, value, callback) => {
    //        if (!isURL(value)) {
    //          callback(new Error('必须以http,https开头,且需符合URL规范,检查格式是否正确'))
    //        } else {
    //          callback()
    //        }
    //      }
    var validateCh = (rule, value, callback) => {
      if (isIncludeCh(value) || isSpecial(value)) {
        callback(new Error('不可含中文、特殊字符'))
      } else {
        callback()
      }
    }
    return {
      visible: false,
      formDisabled: false,
      dataForm: {
        id: '',
        bm: '',
        mch1: '',
        mch1Path: [],
        mch2: '',
        xlNum: '',
        mch3: '2',
        mch4: '1',
        zbl: '0',
        zbx: [],
        sqla: '',
        sqlb: '',
        sqlc: '',
        lrsm: '',
        xgsm: '',
        bz: '',
        sjly: '1',
        qqfs: '1',
        dataStatus: '',
        jkdz: '',
        mmkj: '',
        ffm: '',
        cssc: '30000',
        sjzd: '',
        cdzd: '',
        cdz: '',
        msgzd: '',
        isjson: '0',
        jsondata: '',
        hdcs: [
          {
            keylabel: '',
            keyval: '',
          },
        ],
        zbcs: [
          {
            csm: '',
            csz: '',
            csms: '',
            sfbt: false,
          },
        ],
        type: '',
      },
      stypes: [
        {
          dictLabel: '',
          dictValue: '',
        },
      ],
      types: [
        {
          dictLabel: '',
          dictValue: '',
        },
      ],
      sjlys: [
        {
          dictLabel: '',
          dictValue: '',
        },
      ],
      qqfss: [
        {
          dictLabel: '',
          dictValue: '',
        },
      ],
      ymzts: [
        {
          dataName: '',
          dataCode: '',
        },
      ],
      status: [
        { value: 1, label: '停止' },
        { value: 0, label: '正常' },
      ],
      stateArr: [
        { value: '1', label: '是' },
        { value: '0', label: '否' },
      ],
      statuList: [
        { value: '1', label: '在线数据' },
        { value: '2', label: '离线数据' },
        { value: '3', label: '模拟数据' },
        { value: '4', label: '多种数据' },
      ],
      // 指标项树选项
      indexOptions: [],
      // 指标项选项
      zbxOptions: [],
      // 指标项选项label
      zbxLabels: [],
      // 指标类选项变更次数
      zblChangeNum: 0,
      bmReadonly: false, // 编码元素是否只读
      xgsmIsShow: false, // 是否展示修改描述元素
      dataListLoading: false,
      zbDebugVisible: false,
      zbxDialogVisible: false,
      sql_ele_show: true, // 是否展示sql元素
      jkdz_ele_show: false, // 是否展示接口地址元素
      mmkj_ele_show: false, // 是否展示命名空间元素
      ffm_ele_show: false, // 是否展示方法名元素
      key_ele_show: false, // 是否展示第三方接口请求key元素
      cssc_ele_show: false, // 是否展示第三方接口超时限制元素
      sjzd_ele_show: false, // 是否展示第三方接口返回数据字段名元素
      cdzd_ele_show: false, // 是否展示第三方接口返回状态码字段名元素
      msgzd_ele_show: false, // 是否展示第三方接口返回消息提示字段名元素
      debug_but_ele_show: true, // 是否展示指标调试按钮元素
      jksj_judge_show: false,
      validateUrl: (rule, value, callback) => {
        // 接口地址格式
        if (!isURL(value)) {
          callback(
            new Error('必须以http,https开头,且需符合URL规范,检查格式是否正确')
          )
        } else {
          callback()
        }
      },
      dataRule: {
        bm: [{ required: true, message: '指标编码不能为空', trigger: 'blur' }],
        mch1Path: [
          { required: true, message: '指标领域不能为空', trigger: 'change' },
        ],
        jkdz: [
          { required: false, message: '接口地址不能为空', trigger: 'blur' },
        ],
        mmkj: [
          { required: false, message: '命名空间不能为空', trigger: 'blur' },
        ],
        ffm: [{ required: false, message: '方法名不能为空', trigger: 'blur' }],
        sqla: [{ required: true, message: '公开sql不能为空', trigger: 'blur' }],
        lrsm: [
          { required: true, message: '录入说明不能为空', trigger: 'blur' },
        ],
        xgsm: [
          { required: false, message: '修改说明不能为空', trigger: 'blur' },
        ],
        bz: [{ required: true, message: '备注不能为空', trigger: 'blur' }],
        cdzd: [
          {
            required: false,
            message: '请根据接口文档指定状态码字段',
            trigger: 'blur',
          },
        ],
        cdz: [
          {
            required: false,
            message: '请根据接口文档填写正常返回状态码',
            trigger: 'blur',
          },
          { validator: validateCh, trigger: 'blur' },
        ],
        msgzd: [
          {
            required: false,
            message: '请根据接口文档指定消息字段',
            trigger: 'blur',
          },
        ],
        csm: [{ validator: validateCh, trigger: 'blur' }],
        keylabel: [{ validator: validateCh, trigger: 'blur' }],
      },
    }
  },
  watch: {
    // 'dataForm.zbl': 'zblChange'
  },
  created() {
    this.getTreeselect()
    this.getDicts('screen_dataStyle').then((response) => {
      this.stypes = response.data
    })
    this.getDicts('screen_returnType').then((response) => {
      this.types = response.data
    })
    this.getDicts('screen_dataSource').then((response) => {
      this.sjlys = response.data
    })
    this.getDicts('screen_methTypes').then((response) => {
      this.qqfss = response.data
    })
    this.getDicts('tb_ywzbpzb_status').then((response) => {
      this.statuss = response.data
    })
  },
  methods: {
    // 初始化字段
    initializeFields() {
      this.dataForm.status = 0
      this.dataForm.id = ''
      this.dataForm.bm = ''
      this.dataForm.mch1 = ''
      this.dataForm.mch1Path = []
      this.dataForm.mch2 = ''
      this.dataForm.xlNum = ''
      this.dataForm.mch3 = '2'
      this.dataForm.mch4 = '1'
      this.dataForm.zbl = '0'
      this.dataForm.zbx = []
      this.dataForm.sqla = ''
      this.dataForm.sqlb = ''
      this.dataForm.sqlc = ''
      this.dataForm.lrsm = ''
      this.dataForm.xgsm = ''
      this.dataForm.bz = ''
      this.dataForm.sjly = '1'
      this.dataForm.isjson = '0'
      this.dataForm.qqfs = '1'
      this.dataForm.dataStatus = ''
      this.dataForm.jkdz = ''
      this.dataForm.mmkj = ''
      this.dataForm.ffm = ''
      this.dataForm.cssc = '30000'
      this.dataForm.sjzd = ''
      this.dataForm.cdzd = ''
      this.dataForm.cdz = ''
      this.dataForm.msgzd = ''
      this.dataForm.hdcs = [
        {
          keylabel: '',
          keyval: '',
        },
      ]
      this.dataForm.zbcs = [
        {
          csm: '',
          csz: '',
          csms: '',
          sfbt: false,
        },
      ]
      this.dataForm.type = ''
    },
    // 接收父页面传递的值
    init(id, subType) {
      this.formDisabled = subType == 'cat'
      this.zblChangeNum = 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        // this.dataForm = this.resetDataForm
        this.initializeFields()
        if (subType != 'add') {
          this.getZbx(id)
        }
        // this.
        if (id.length > 0) {
          getZbData({ id: id }).then((response) => {
            if (response && response.successful) {
              this.dataForm = response.data
              this.dataForm.jsondata =
                response.data.zbcs[0] && response.data.zbcs[0].jsondata
              this.dataForm.isjson =
                response.data.zbcs[0] && response.data.zbcs[0].isjson + ''
              const zbcsArr = response.data.zbcs
              if (zbcsArr === null || zbcsArr.length === 0) {
                this.dataForm.zbcs.push({
                  csm: '',
                  csz: '',
                  csms: '',
                  sfbt: false,
                })
              } else {
                for (var i = 0; i < zbcsArr.length; i++) {
                  if (zbcsArr[i].sfbt === 0) {
                    zbcsArr[i].sfbt = false
                  } else {
                    zbcsArr[i].sfbt = true
                  }
                }
                this.dataForm.zbcs = zbcsArr
              }

              const keyArr = response.data.hdcs
              if (keyArr === null || keyArr.length === 0) {
                this.dataForm.hdcs.push({
                  keylabel: '',
                  keyval: '',
                })
              } else {
                this.dataForm.hdcs = keyArr
              }
              if (response.data.zbx != null) {
                const zbxArr = response.data.zbx.split(',')
                for (let i = 0; i < zbxArr.length; i++) {
                  // zbxArr[i] = parseInt(zbxArr[i]);
                  zbxArr[i] = zbxArr[i]
                }
                this.dataForm.zbx = zbxArr
              } else {
                this.dataForm.zbx = []
              }
              this.sjlyOnChange(response.data.sjly)
            } else {
              console.log('ERROR', response)
              this.$message.error('操作失败')
            }
          })
          this.bmReadonly = true
          this.xgsmIsShow = true
          this.dataRule.xgsm[0].required = true
        } else {
          this.bmReadonly = false
          this.xgsmIsShow = false
          this.dataRule.xgsm[0].required = false
          this.sjlyOnChange('')
        }
        this.getDpztList()
        // this.getStyle()
        // this.getType()
        // this.getSjlys()
        // this.getQqfss()
        this.dataForm.id = id
        this.dataForm.type = subType
      })
    },
    /** 转换指标项数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children,
      }
    },
    /** 查询指标下拉树结构 */
    getTreeselect() {
      // listAllIndex({'indexType': 1}).then(response => {
      //   this.indexOptions = [];
      //   const data = { indexId: 0, indexName: '顶级节点', children: [] };
      // data.children = this.handleTree(response.data, "indexId", "parentId");
      // for(var i=0;i<data.children.length;i++){
      //   if(data.children[i].status==1){
      //   delete data.children[i];
      //   }
      // }
      // this.indexOptions.push(data);
      // });
      listTreeCategory().then((res) => {
        // console.log(res);
        // this.indexParentOptions = listToTree('id','parentId',res.data,'name');
        // console.log(this.indexParentOptions);
        this.indexOptions = []
        const data = { id: 0, name: '顶级节点', children: [] }
        data.children = handleTree(res.data, 'id', 'parentId')
        this.indexOptions.push(data)
        // console.log(this.indexOptions);
      })
    },
    // 验证指标编码是否已存在
    validateSame() {
      if (this.dataForm.type === 'add') {
        isSameBm({ bm: this.dataForm.bm }).then((response) => {
          if (
            response.successful &&
            response.data != null &&
            response.data > 0
          ) {
            this.messageHint(this.dataForm.bm)
          }
          if (
            response.successful &&
            response.data != null &&
            response.data === 0
          ) {
            this.dataFormSubmit()
          }
          if (!response.successful) {
            console.log('ERROR', response)
            this.$message.error('操作失败')
          }
        })
      } else {
        this.dataFormSubmit()
      }
    },
    messageHint(value) {
      this.$message({
        dangerouslyUseHTMLString: true,
        message:
          '<strong style="color: #ff2222">指标编码' +
          value +
          '已存在!</strong>',
      })
    },
    // 表单提交
    dataFormSubmit() {
      const zbcs = this.dataForm.zbcs
      const zbcsArr = []

      for (let i = 0; i < zbcs.length; i++) {
        if (zbcs[i].csm.length > 0) {
          zbcsArr.push({
            csm: zbcs[i].csm,
            csz: zbcs[i].csz,
            csms: zbcs[i].csms,
            sfbt: zbcs[i].sfbt ? 1 : 0,
            isjson: this.dataForm.isjson,
            jsondata: this.dataForm.jsondata,
          })
        }
      }
      let kl = ''
      let kv = ''
      if (this.dataForm.sjly === '2' || this.dataForm.sjly === '3') {
        const hearderCs = this.dataForm.hdcs
        for (let i = 0; i < hearderCs.length; i++) {
          if (
            hearderCs[i].keylabel.length > 0 &&
            hearderCs[i].keyval.length > 0
          ) {
            if (kl.length > 0) {
              kl += ',' + hearderCs[i].keylabel
              kv += ',' + hearderCs[i].keyval
            } else {
              kl += hearderCs[i].keylabel
              kv += hearderCs[i].keyval
            }
          }
        }
      }
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const params = {}
          params.id = this.dataForm.id || undefined
          params.bm = this.dataForm.bm
          params.mch1 = this.dataForm.mch1
          params.mch1Path = this.dataForm.mch1Path
          params.mch2 = this.dataForm.mch2
          params.xlNum = this.dataForm.xlNum
          params.mch3 = this.dataForm.mch3
          params.mch4 = this.dataForm.mch4
          params.zbl = this.dataForm.zbl
          params.zbx = this.dataForm.zbx.join(',')
          params.sqla = this.dataForm.sqla
          params.sqlb = this.dataForm.sqlb
          params.sqlc = this.dataForm.sqlc
          params.lrsm = this.dataForm.lrsm
          params.xgsm = this.dataForm.xgsm
          params.bz = this.dataForm.bz
          params.sjly = this.dataForm.sjly
          params.qqfs = this.dataForm.qqfs
          params.dataStatus = this.dataForm.dataStatus
          params.jkdz = this.dataForm.jkdz
          params.mmkj = this.dataForm.mmkj
          params.ffm = this.dataForm.ffm
          params.cssc = this.dataForm.cssc
          params.sjzd = this.dataForm.sjzd
          params.cdzd = this.dataForm.cdzd
          params.cdz = this.dataForm.cdz
          params.msgzd = this.dataForm.msgzd
          params.keylabel = kl
          params.keyval = kv
          params.zbcs = zbcsArr
          params.status = this.dataForm.status

          if (this.dataForm.type === 'add') {
            addZb(params).then((response) => {
              if (response.successful) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  },
                })
              } else {
                console.log('ERROR', response)
                this.$message.error('操作失败')
              }
            })
          } else {
            updateZb(params).then((response) => {
              if (response.successful) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  },
                })
              } else {
                console.log('ERROR', response)
                this.$message.error('操作失败')
              }
            })
          }
        }
      })
    },
    // 添加参数
    addCs() {
      this.dataForm.zbcs.push({
        csm: '',
        csz: '',
        csms: '',
        sfbt: false,
      })
    },
    // 删除参数
    deleteCs(item) {
      var index = this.dataForm.zbcs.indexOf(item)
      if (index !== -1) {
        this.dataForm.zbcs.splice(index, 1)
      }
    },
    // 添加请求头参数
    addHeaderCs() {
      this.dataForm.hdcs.push({
        keylabel: '',
        keyval: '',
      })
    },
    // 删除请求头参数
    deleteHeaderCs(item) {
      var index = this.dataForm.hdcs.indexOf(item)
      if (index !== -1) {
        this.dataForm.hdcs.splice(index, 1)
      }
    },
    // 调试
    debugScript() {
      if (this.dataForm.type !== 'add') {
        this.dataRule.xgsm[0].required = false
      }
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.dataForm.type !== 'add') {
            this.dataRule.xgsm[0].required = true
          }
          this.dataListLoading = true
          var params = {
            bm: this.dataForm.bm,
            sjly: this.dataForm.sjly,
          }
          // 库表
          if (this.dataForm.sjly === '1') {
            params.mch3 = this.dataForm.mch3
            params.sqla =
              btoa(unescape(encodeURIComponent(this.dataForm.sqla))) + '1'
            // 接口
          } else {
            let keylabel = ''
            let keyval = ''
            const hearderCs = this.dataForm.hdcs
            for (let i = 0; i < hearderCs.length; i++) {
              if (
                hearderCs[i].keylabel.length > 0 &&
                hearderCs[i].keyval.length > 0
              ) {
                if (keylabel.length > 0) {
                  keylabel += ',' + hearderCs[i].keylabel
                  keyval += ',' + hearderCs[i].keyval
                } else {
                  keylabel += hearderCs[i].keylabel
                  keyval += hearderCs[i].keyval
                }
              }
            }
            params.jkdz = this.dataForm.jkdz
            params.qqfs = this.dataForm.qqfs
            params.dataStatus = this.dataForm.dataStatus
            params.keylabel = this.dataForm.keylabel
            params.keyval = this.dataForm.keyval
            params.cssc = this.dataForm.cssc
            params.sjzd = this.dataForm.sjzd
            params.mmkj = this.dataForm.mmkj
            params.ffm = this.dataForm.ffm
            params.cdzd = this.dataForm.cdzd
            params.cdz = this.dataForm.cdz
            params.msgzd = this.dataForm.msgzd
            params.keylabel = keylabel
            params.keyval = keyval
            params.zbJsonCsDtos = this.dataForm.jsondata
            params.isjson = this.dataForm.isjson
          }
          const zbcs = this.dataForm.zbcs
          const zbcsArr = []
          for (let i = 0; i < zbcs.length; i++) {
            if (zbcs[i].csm.length > 0) {
              zbcsArr.push({
                csm: zbcs[i].csm,
                csz: zbcs[i].csz,
                isjson: this.dataForm.isjson,
                jsondata: this.dataForm.jsondata,
              })

              console.log(zbcs[i] + '.....' + zbcsArr)
            }
          }
          params.zbcs = zbcsArr
          const loadingInstance = Loading.service({
            text: '正在获取调试结果。。。',
          })
          debugScript(params)
            .then((response) => {
              loadingInstance.close()
              this.debugHandle(response.totalTimes, response.data)
            })
            .catch(function (error) {
              loadingInstance.close()
            })
        }
      })
    },
    // 将调试结果传递给弹窗
    debugHandle(totalTimes, tsjg) {
      this.zbDebugVisible = true
      this.$nextTick(() => {
        this.dataListLoading = false
        this.$refs.zbDebug.init(totalTimes, tsjg)
      })
    },
    showZbx() {
      this.zbxDialogVisible = true
      this.$nextTick(() => {
        this.$refs.zbxDialog.init()
      })
    },
    getZbxList(val) {
      console.log(val)
      const str = []
      val.forEach((item) => {
        str.push(item.indicatorId)
      })
      this.dataForm.zbx = str
      this.zbxOptions = val
    },
    // 格式化sql脚本
    sqlFormatter(sqlScript, sqlEleType) {
      // 获取格式化SQL
      var formatSql = sqlFormatter.format(sqlScript, { language: 'sql' })
      if (sqlEleType === 1) {
        this.dataForm.sqla = formatSql
          .replace('<\n', '<')
          .replace('\n>', '>')
          .replace('< ', '<')
          .replace(' >', '>')
      }
      if (sqlEleType === 2) {
        this.dataForm.sqlb = formatSql
      }
      if (sqlEleType === 3) {
        this.dataForm.sqlc = formatSql
      }
    },
    // 监听数据来源下拉框值改变，隐藏或显示相关的form表单
    sjlyOnChange(sjly) {
      var sjlyVal = '1'
      if (sjly === null || sjly === '') {
        sjlyVal = this.dataForm.sjly
      } else {
        sjlyVal = sjly
      }
      if (sjlyVal === '1') {
        this.dataRule.jkdz = []
        this.dataRule.jkdz.push({
          required: false,
          message: '接口地址不能为空',
          trigger: 'blur',
        })
        // this.dataRule.jkdz.splice(1, 1)
        this.dataRule.sqla[0].required = true
        this.dataRule.mmkj[0].required = false
        this.dataRule.ffm[0].required = false
        this.dataRule.cdzd[0].required = false
        this.dataRule.cdz[0].required = false
        this.dataRule.msgzd[0].required = false
        this.sql_ele_show = true
        this.jkdz_ele_show = false
        this.mmkj_ele_show = false
        this.ffm_ele_show = false
        this.key_ele_show = false
        this.cssc_ele_show = false
        this.sjzd_ele_show = false
        this.cdzd_ele_show = false
        this.msgzd_ele_show = false
        this.jksj_judge_show = false
        // this.debug_but_ele_show = true
      }
      if (sjlyVal === '2' || sjlyVal === '4') {
        if (sjlyVal === '2') {
          this.mmkj_ele_show = false
          this.ffm_ele_show = false
          this.dataRule.mmkj[0].required = false
          this.dataRule.ffm[0].required = false
          this.jksj_judge_show = true
        } else {
          this.mmkj_ele_show = true
          this.ffm_ele_show = true
          this.dataRule.mmkj[0].required = true
          this.dataRule.ffm[0].required = true
          this.jksj_judge_show = false
        }
        this.dataRule.jkdz = []
        this.dataRule.jkdz.push({
          required: true,
          message: '接口地址不能为空',
          trigger: 'blur',
        })
        // this.dataRule.jkdz[0].required = true
        this.dataRule.jkdz.push({
          validator: this.validateUrl,
          trigger: 'blur',
        })
        this.dataRule.sqla[0].required = false
        this.dataRule.cdzd[0].required = true
        this.dataRule.cdz[0].required = true
        this.dataRule.msgzd[0].required = true
        this.sql_ele_show = false
        this.jkdz_ele_show = true
        this.key_ele_show = true
        this.cssc_ele_show = true
        this.sjzd_ele_show = true
        this.cdzd_ele_show = true
        this.msgzd_ele_show = true

        // this.debug_but_ele_show = false
      }
      if (sjlyVal === '3' || sjlyVal === '5') {
        if (sjlyVal === '3') {
          this.mmkj_ele_show = false
          this.ffm_ele_show = false
          this.dataRule.mmkj[0].required = false
          this.dataRule.ffm[0].required = false
          this.jksj_judge_show = true
        } else {
          this.mmkj_ele_show = true
          this.ffm_ele_show = true
          this.dataRule.mmkj[0].required = true
          this.dataRule.ffm[0].required = true
          this.jksj_judge_show = false
        }
        this.dataRule.jkdz = []
        this.dataRule.jkdz.push({
          required: true,
          message: '接口地址不能为空',
          trigger: 'blur',
        })
        // this.dataRule.jkdz[0].required = true
        this.dataRule.jkdz.push({
          validator: this.validateUrl,
          trigger: 'blur',
        })
        this.dataRule.sqla[0].required = false
        this.dataRule.cdzd[0].required = false
        this.dataRule.cdz[0].required = false
        this.dataRule.msgzd[0].required = false
        this.sql_ele_show = false
        this.jkdz_ele_show = true
        this.key_ele_show = true
        this.cssc_ele_show = true
        this.sjzd_ele_show = true
        this.cdzd_ele_show = false
        this.msgzd_ele_show = false
        // this.debug_but_ele_show = false
      }
    },
    // 获取页面专题
    getDpztList() {
      getDpztList({}).then((response) => {
        // this.ymzts = response.data
        this.ymzts = listToTree('id', 'parentCode', response.data)
      })
    },
    // 指标类选项变更操作
    zblChange() {
      console.log(this.zblChangeNum)
      console.log(this.dataForm)
      // >1排除回填变更当次
      if (this.zblChangeNum > 1) {
        this.dataForm.zbx = []
      }
      getIndexItemList({
        indexId: this.dataForm.zbl == null ? 0 : this.dataForm.zbl,
      }).then((response) => {
        this.zbxOptions = response.data
      })
      this.zblChangeNum++
    },
    getZbx(id) {
      getZbx({ id: id }).then((res) => {
        this.zbxOptions = res.data
      })
    },
    // 根据指标项生成半自动sql
    selectBox(val, label) {
      console.log(this.dataForm.zbx)
      if (this.dataForm.sjly == '1' && this.dataForm.type == 'add') {
        if (this.dataForm.zbx.indexOf(val) == -1) {
          if (this.zbxLabels.indexOf(label) != -1) {
            this.zbxLabels.splice(this.zbxLabels.indexOf(label), 1)
          }
        } else {
          this.zbxLabels.push(label)
        }
        let sql = 'SETECT\n'
        const zbxLabelsLength = this.zbxLabels.length
        for (let i = 0; i < zbxLabelsLength; i++) {
          if (i != zbxLabelsLength - 1) {
            sql += '* AS ' + this.zbxLabels[i].split('-')[1] + ',\n'
          } else {
            sql += '* AS ' + this.zbxLabels[i].split('-')[1] + '\n'
          }
        }
        sql += 'FROM *'
        this.dataForm.sqla = sql
      }
    },
  },
}
</script>
