// Flexbox工具类
.flex-container {
  display: flex;
  
  &.flex-column {
    flex-direction: column;
  }
  
  &.flex-row {
    flex-direction: row;
  }
}

.flex-item {
  &.flex-grow {
    flex: 1 1 0;
    min-height: 0; // 关键：允许收缩
    min-width: 0;  // 关键：允许收缩
  }
  
  &.flex-shrink-0 {
    flex-shrink: 0;
  }
  
  &.overflow-auto {
    overflow: auto;
  }
  
  &.overflow-y-auto {
    overflow-y: auto;
  }
}

// 修复flexbox overflow的通用类
.flex-overflow-fix {
  min-height: 0;
  min-width: 0;
  flex: 1 1 0;
} 