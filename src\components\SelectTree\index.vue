<!-- el-select & el-tree 下拉树形选择 -->
<template>
  <div>
    <el-select ref="selectTree" v-model="localValue" :multiple="multiple" :clearable="clearable" @clear="clearHandle" :disabled="disabled">
      <el-option v-for="option in options" :key="option[props.value]" :value="option[props.value]">
        <el-tree
          :data="options"
          :props="props"
          :node-key="props.value"
          @node-click="handleNodeClick"
          :accordion="accordion"
        >
          <span slot-scope="{ data }">
            <i :class="[data.color != null ? 'ification_col' : '']" :style="{'background-color': data.color}"></i>&nbsp;&nbsp;{{ data.label }}
          </span>
        </el-tree>
      </el-option>
    </el-select>
  </div>
</template>

<script>export default {
  name: "el-tree-select",
  props: {
    // 配置项
    props: {
      type: Object,
      default: () => ({
        value: 'id',
        children: 'children',
        label: 'name'
      })
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 选项列表数据(树形结构的对象数组)
    options: {
      type: Array,
      default: () => []
    },
    // 初始值（单选或多选）
    value: {
      type: [String, Number, Object, Array], // 允许多种类型
      default: null
    },
    // 可清空选项
    clearable: {
      type: Boolean,
      default: true
    },
    // 自动收起
    accordion: {
      type: Boolean,
      default: false
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      localValue: this.value || (this.multiple ? [] : null), // 使用 value 或者默认值
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(newValue) {
        this.localValue = newValue;
      }
    },
    localValue: {
      handler(newValue) {
        this.$emit('input', newValue);
      }
    }
  },
  methods: {
    // 清除选中
    clearHandle() {
      this.localValue = this.multiple ? [] : null;
      this.clearSelected();
      this.$emit('input', this.localValue);
    },
    // 切换选项
    handleNodeClick(node) {
      if (this.multiple) {
        // 多选（判重后添加）
        if (!this.localValue.includes(node[this.props.label])) {
          this.localValue.push(node[this.props.label]);
        }
      } else {
        // 单选
        this.localValue = node[this.props.label];
      }
      this.$emit('input', this.localValue);
    },
    // 清空选中样式
    clearSelected() {
      const treeNodes = this.$refs.selectTree.$el.querySelectorAll('.el-tree-node');
      treeNodes.forEach(node => node.classList.remove('is-current'));
    },
  }
}
</script>
<style scoped>
.el-scrollbar .el-scrollbar__view .el-select-dropdown__item {
  height: auto;
  max-height: 300px;
  padding: 0;
  overflow: hidden;
  overflow-y: auto;
}
.el-select-dropdown__item.selected {
  font-weight: normal;
}
.el-tree .el-tree-node__content {
  height: auto;
  padding: 0 20px;
}
.el-tree-node__label {
  font-weight: normal;
}
.el-tree >>> .is-current .el-tree-node__label {
  color: #409EFF;
  font-weight: 700;
}
.el-tree >>> .is-current .el-tree-node__children .el-tree-node__label {
  color: #606266;
  font-weight: normal;
}
.el-popper {
  z-index: 9999;
}
.ification_col {
  width: 20px;
  height: 10px;
  display: inline-block;
}
</style>
