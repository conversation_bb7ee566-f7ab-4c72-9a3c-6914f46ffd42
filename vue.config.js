"use strict";
const path = require("path");
const defaultSettings = require("./src/settings.js");
const webpack = require("webpack");
const TerserWebpackPlugin = require("terser-webpack-plugin");
const CompressionWebpackPlugin = require("compression-webpack-plugin");

function resolve(dir) {
  return path.join(__dirname, dir);
}

const name = defaultSettings.title || "安陆智能体"; // 标题

const port = process.env.port || process.env.npm_config_port || 9060; // 端口

const isProd = process.env.NODE_ENV === "production";

// 生成时间戳
const timestamp = new Date().getTime();

// vue.config.js 配置说明
//官方vue.config.js 参考文档 https://cli.vuejs.org/zh/config/#css-loaderoptions
// 这里只列一部分，具体配置参考文档
module.exports = {
  // 部署生产环境和开发环境下的URL。
  // 默认情况下，Vue CLI 会假设你的应用是被部署在一个域名的根路径上
  // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
  publicPath: process.env.NODE_ENV === "production" ? "/" : "/",
  // 在npm run build 或 yarn build 时 ，生成文件的目录名称（要和baseUrl的生产环境路径一致）（默认dist）
  outputDir: "dist",
  // 用于放置生成的静态资源 (js、css、img、fonts) 的；（项目打包之后，静态资源会放在这个文件夹下）
  assetsDir: "static",
  // 是否开启eslint保存检测，有效值：ture | false | 'error'
  lintOnSave: process.env.NODE_ENV === "development",
  // 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。
  productionSourceMap: false,
  // webpack-dev-server 相关配置
  devServer: {
    host: "0.0.0.0",
    port: port,
    open: true,
    proxy: {
      // detail: https://cli.vuejs.org/config/#devserver-proxy
      [process.env.VUE_APP_BASE_API]: {
        target: `http://***************:9002/alu-api`,
        // target: "http://************:9302/alu-api", //范俊伟
        changeOrigin: true,
        pathRewrite: {
          ["^" + process.env.VUE_APP_BASE_API]: "",
        },
      },
      // AI 服务代理，解决浏览器跨域
      "/ai-api": {
        // target: 'http://************:8084',
        target: "http://*************:8082",
        changeOrigin: true,
        pathRewrite: {
          "^/ai-api": "",
        },
      },
      "/transcribe-ws1": {
        // target: 'http://************:8084',
        target: "http://*************:8082/transcribe-ws1",
        changeOrigin: true,
        pathRewrite: {
          "^/transcribe-ws1": "",
        },
      },
    },
    disableHostCheck: true,
  },
  configureWebpack: {
    name: name,
    resolve: {
      alias: {
        "@": resolve("src"),
      },
    },
    // 添加输出文件名配置，包含时间戳
    output: isProd
      ? {
          filename: `static/js/[name].${timestamp}.js`,
          chunkFilename: `static/js/[name].${timestamp}.js`,
        }
      : {},
    plugins: isProd
      ? [
          new webpack.IgnorePlugin(/^\.\/locale$/, /moment$/),
          new TerserWebpackPlugin({
            terserOptions: {
              compress: {
                warnings: true,
                drop_console: true,
                drop_debugger: true,
                pure_funcs: ["console.log", "console.table"], // 删除console
              },
            },
          }),
          new CompressionWebpackPlugin({
            test: /\.js$|\.html$|\.css$/u,
            threshold: 4096, // 超过 4kb 压缩
          }),
        ]
      : [],
  },
  chainWebpack(config) {
    config.plugins.delete("preload"); // TODO: need test
    config.plugins.delete("prefetch"); // TODO: need test

    // set svg-sprite-loader
    config.module.rule("svg").exclude.add(resolve("src/assets/icons")).end();
    config.module
      .rule("icons")
      .test(/\.svg$/)
      .include.add(resolve("src/assets/icons"))
      .end()
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader")
      .options({
        symbolId: "icon-[name]",
      })
      .end();

    config.when(process.env.NODE_ENV !== "development", (config) => {
      // 配置CSS文件名包含时间戳
      config.plugin("extract-css").tap((args) => {
        args[0].filename = `static/css/[name].${timestamp}.css`;
        args[0].chunkFilename = `static/css/[name].${timestamp}.css`;
        return args;
      });

      config
        .plugin("ScriptExtHtmlWebpackPlugin")
        .after("html")
        .use("script-ext-html-webpack-plugin", [
          {
            // `runtime` must same as runtimeChunk name. default is `runtime`
            inline: /runtime\..*\.js$/,
          },
        ])
        .end();
      config.optimization.splitChunks({
        chunks: "all",
        cacheGroups: {
          libs: {
            name: "chunk-libs",
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: "initial", // only package third parties that are initially dependent
          },
          elementUI: {
            name: "chunk-elementUI", // split elementUI into a single package
            priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
            test: /[\\/]node_modules[\\/]_?element-ui(.*)/, // in order to adapt to cnpm
          },
          commons: {
            name: "chunk-commons",
            test: resolve("src/components"), // can customize your rules
            minChunks: 3, //  minimum common number
            priority: 5,
            reuseExistingChunk: true,
          },
        },
      });
      config.optimization.runtimeChunk("single"),
        {
          from: path.resolve(__dirname, "./public/robots.txt"), //防爬虫文件
          to: "./", //到根目录下
        };
    });
  },
};
