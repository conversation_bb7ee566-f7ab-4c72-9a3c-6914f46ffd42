<template>
  <div class="app-container">
    <el-form
      :inline="true"
      :model="dataForm"
      ref="dataForm"
      v-show="showSearch"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item prop="indexid">
        <el-input
          v-model="dataForm.indexid"
          placeholder="指标编码"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item prop="userName">
        <el-input
          v-model="dataForm.userName"
          placeholder="使用者"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item prop="clientip">
        <el-input
          v-model="dataForm.clientip"
          placeholder="使用IP"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-table :data="dataList" v-loading="dataListLoading" style="width: 100%">
      <el-table-column
        prop="indexid"
        header-align="center"
        align="center"
        width="150"
        label="指标编码"
      ></el-table-column>
      <el-table-column
        prop="dqzt"
        header-align="center"
        align="center"
        label="当前状态"
      ></el-table-column>
      <el-table-column
        prop="calldate"
        header-align="center"
        align="center"
        label="最近一次交换开始时间"
      ></el-table-column>
      <el-table-column
        prop="calldateEnd"
        header-align="center"
        align="center"
        width="220"
        label="最近一次交换结束时间"
      ></el-table-column>
      <el-table-column
        prop="callstatus"
        header-align="center"
        align="center"
        label="调用状态"
      ></el-table-column>
      <el-table-column
        prop="userName"
        header-align="center"
        align="center"
        label="使用者"
      ></el-table-column>
      <el-table-column
        prop="clientip"
        header-align="center"
        align="center"
        label="使用IP"
      ></el-table-column>
      <el-table-column
        header-align="center"
        align="center"
        width="240"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['screen:zbgl:edit']"
            type="text"
            size="small"
            @click="handleDetail(scope.row.indexid, 'cat')"
          >
            查看更多
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="pageNum"
      :limit.sync="pageSize"
      @pagination="getDataList"
    />

    <!-- 弹窗 -->
    <log-list
      v-if="logListVisible"
      ref="logList"
      @refreshDataList="getDataList"
    ></log-list>
  </div>
</template>

<script>
import { getApiLastList } from '@/api/screen/zbgl/jkrzcs.js'
import logListzb from './zbgl-log-list.vue'

export default {
  data() {
    return {
      dataForm: {
        indexid: '',
        userName: '',
        clientip: '',
      },
      // 显示搜索条件
      showSearch: true,
      dataList: [],
      total: 0,
      pageNum: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      //弹窗
      logListVisible: false,
    }
  },
  components: {
    logList: logListzb,
  },
  mounted() {
    // this.getDpztList()
  },
  created() {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      getApiLastList({
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        indexid: this.dataForm.indexid,
        userName: this.dataForm.userName,
        clientip: this.dataForm.clientip,
      }).then((response) => {
        this.dataList = response.rows
        this.total = response.total
        this.dataListLoading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getDataList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('dataForm')
      this.handleQuery()
    },

    // 查看接口详情
    handleDetail(bm) {
      this.logListVisible = true
      this.$nextTick(() => {
        this.$refs.logList.init(bm)
      })
    },
  },
}
</script>
