// 移动端适配样式
@media (max-width: 768px) {
  // 全局移动端样式
  .mobile-page {
    font-size: 14px;
    
    // 隐藏桌面端元素
    .desktop-only {
      display: none !important;
    }
    
    // 移动端专用元素
    .mobile-only {
      display: block !important;
    }
    
    // 按钮适配
    .el-button {
      &.mobile-btn {
        padding: 8px 16px;
        font-size: 14px;
        border-radius: 20px;
      }
      
      &.mobile-btn-small {
        padding: 6px 12px;
        font-size: 12px;
        border-radius: 16px;
      }
    }
    
    // 输入框适配
    .el-input {
      &.mobile-input {
        .el-input__inner {
          height: 40px;
          line-height: 40px;
          font-size: 14px;
          border-radius: 20px;
        }
      }
    }
    
    // 卡片适配
    .mobile-card {
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
      
      &:active {
        transform: scale(0.98);
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
      }
    }
    
    // 列表适配
    .mobile-list {
      padding: 0;
      
      .list-item {
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
      }
    }
    
    // 空状态适配
    .mobile-empty {
      padding: 60px 20px;
      text-align: center;
      
      .empty-icon {
        font-size: 64px;
        color: #dcdfe6;
        margin-bottom: 16px;
      }
      
      .empty-text {
        font-size: 16px;
        color: #909399;
        margin-bottom: 24px;
        line-height: 1.5;
      }
    }
    
    // 加载状态适配
    .mobile-loading {
      text-align: center;
      padding: 20px;
      
      .loading-text {
        color: #409eff;
        font-size: 14px;
        
        i {
          margin-right: 8px;
        }
      }
    }
    
    // 下拉刷新适配
    .pull-refresh-indicator {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      
      .indicator-content {
        color: #409eff;
        font-size: 14px;
      }
    }
  }
}

// 超小屏幕适配 (iPhone SE等)
@media (max-width: 375px) {
  .mobile-page {
    font-size: 13px;
    
    .mobile-header {
      padding: 10px 12px;
      
      .header-title {
        font-size: 16px;
      }
    }
    
    .mobile-search {
      padding: 10px 12px;
    }
    
    .mobile-content {
      padding: 0 12px 12px;
      
      .mobile-card {
        padding: 12px;
        margin-bottom: 8px;
        
        .card-header {
          .card-title {
            font-size: 15px;
          }
          
          .card-time {
            font-size: 11px;
          }
        }
        
        .card-content {
          .content-preview {
            font-size: 13px;
          }
        }
        
        .card-footer {
          .el-button {
            padding: 3px 6px;
            font-size: 11px;
          }
        }
      }
    }
  }
}

// 横屏适配
@media (max-width: 768px) and (orientation: landscape) {
  .mobile-page {
    .mobile-header {
      padding: 8px 16px;
    }
    
    .mobile-banner {
      margin: 12px 16px;
      padding: 16px;
      
      .banner-content {
        .banner-icon {
          font-size: 36px;
          margin-bottom: 8px;
        }
        
        .banner-title {
          font-size: 16px;
          margin-bottom: 6px;
        }
        
        .banner-desc {
          font-size: 13px;
        }
      }
    }
  }
}

// 触摸优化
@media (pointer: coarse) {
  .mobile-page {
    // 增加触摸目标大小
    .touchable {
      min-height: 44px;
      min-width: 44px;
    }
    
    // 优化按钮触摸
    .el-button {
      min-height: 40px;
      
      &.el-button--mini {
        min-height: 32px;
      }
    }
    
    // 优化链接触摸
    a, .el-link {
      min-height: 44px;
      display: inline-flex;
      align-items: center;
    }
  }
}

// 高分辨率屏幕适配
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .mobile-page {
    .mobile-card {
      border: 0.5px solid #ebeef5;
    }
    
    .mobile-list {
      .list-item {
        border-bottom: 0.5px solid #f0f0f0;
      }
    }
  }
}
