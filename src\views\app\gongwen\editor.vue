<template>
  <div class="gw-editor-page">
    <!-- 顶部栏：标题 + 操作 -->
    <div class="page-header">
      <div class="header-actions">
        <el-button @click="backToList">返回</el-button>
      </div>
      <div class="title-display">{{ form.title || "" }}</div>
      <div class="header-save">
        <el-button
          type="primary"
          :loading="saving || autoSaving"
          @click="saveDraft"
          size="small"
        >
          {{ autoSaving ? '自动保存中...' : (saving ? '保存中...' : '保存') }}
        </el-button>
      </div>
    </div>

    <div class="page-body">
      <!-- 左侧编辑区 -->
      <section class="left">
        <TinyEditor
          v-model="form.content"
          :height="minEditorHeight"
          :showExport="true"
          :exportFilename="`${form.title || '文档'}.docx`"
          :preserveRawHtml="true"
          :autoScroll="true"
          export-method="jquery-plugin"
          @on-paste-image-blocked="onPasteImageBlocked"
          @on-change="handleContentChange"
        />
      </section>

      <!-- 右侧侧栏 -->
      <div class="right">
        <!-- 快捷类型选择 -->
        <div shadow="never" ref="rightPanel" class="panel quick-types document-settings">
          <!-- 大类选择 -->
          <div class="type-category">
            <el-radio-group
              v-model="selectedCategory"
              size="small"
              @change="onCategoryChange"
            >
              <el-radio label="法定公文">法定公文</el-radio>
              <el-radio label="事务文书">事务文书</el-radio>
            </el-radio-group>
          </div>

          <!-- 小类选择 -->
          <div class="type-buttons">
            <div class="type-grid">
              <el-button
                v-for="t in currentTypes"
                :key="t"
                :type="form.type === t ? 'primary' : 'default'"
                :class="[
                  'type-btn',
                  'select1',
                  { 'is-active': form.type === t },
                ]"
                :disabled="!isTypeClickable(t)"
                size="small"
                @click="selectType(t)"
              >
                {{ t }}
              </el-button>
            </div>
          </div>

          <!-- 文章标题（必填） -->
          <div class="setting-item">
            <div class="setting-label">
              <span class="required">*</span>
              文章标题
              <el-tooltip content="必填，建议简明扼要" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </div>
            <el-input
              v-model="form.title"
              placeholder="例如：国庆节放假通知"
              clearable
            />
          </div>

          <!-- 文章篇幅 -->
          <div class="setting-item">
            <div class="setting-label">文章篇幅（字数范围）</div>
            <div class="word-count-range">
              <el-input-number
                v-model="form.minWordCount"
                :min="50"
                :max="form.maxWordCount || 10000"
                :step="50"
                :controls="false"
                size="small"
                placeholder="最少字数"
                controls-position="right"
                style="width: 120px"
                @change="onMinWordCountChange"
              />
              <span class="range-separator">至</span>
              <el-input-number
                v-model="form.maxWordCount"
                :min="form.minWordCount || 100"
                :max="20000"
                :step="50"
                :controls="false"
                size="small"
                placeholder="最多字数"
                controls-position="right"
                style="width: 120px"
                @change="onMaxWordCountChange"
              />
              <span class="word-unit">字</span>
            </div>
          </div>

          <!-- 内容要求 -->
          <div class="setting-item">
            <el-collapse v-model="activeCollapse" accordion>
              <el-collapse-item name="requirements">
                <template #title>
                  <div class="collapse-title">
                    <i class="el-icon-s-flag"></i>
                    内容要求
                  </div>
                </template>
                <div class="requirements-content">
                  <el-input
                    v-model="form.requirements"
                    type="textarea"
                    :autosize="{ minRows: 3 }"
                    :disabled="!form.title"
                    placeholder="内容提要："
                    resize="none"
                  />
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>

          <!-- 大纲 -->
          <div class="setting-item">
            <el-collapse v-model="activeCollapse2" accordion>
              <el-collapse-item name="outline">
                <template #title>
                  <div class="collapse-title">
                    <i class="el-icon-menu"></i>
                    大纲
                  </div>
                </template>
                <div class="outline-content">
                  <el-input
                    v-model="form.outline"
                    type="textarea"
                   :autosize="{ minRows: 4 }"
                    :disabled="!form.title"
                    placeholder="填写要编写内容的大纲"
                    resize="none"
                  />
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>

          <!-- 思考过程显示区域 -->
          <div class="setting-item" v-if="showThinkingProcess">
            <el-collapse v-model="activeCollapse3" accordion>
              <el-collapse-item name="thinking">
                <template #title>
                  <div class="collapse-title">
                    <i class="el-icon-loading" v-if="generating"></i>
                    <i class="el-icon-cpu" v-else></i>
                    思考过程
                    <el-tag v-if="generating" size="mini" type="info"
                      >生成中...</el-tag
                    >
                  </div>
                </template>
                <div class="thinking-content">
                  <div
                    ref="thinkingDisplay"
                    class="thinking-display"
                    :class="{ streaming: generating }"
                  >
                    <div
                      v-if="!thinkingContent && !generating"
                      class="thinking-placeholder"
                    >
                      AI思考过程将在这里显示...
                    </div>
                    <div v-else class="thinking-text">
                      {{ thinkingContent }}
                    </div>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
          <div class="sticky-actions">
            <el-button
              type="success"
              icon="el-icon-magic-stick"
              :loading="generating"
              :disabled="generating"
              @click="generateContent"
              >{{ generating ? "生成中..." : "生成全文" }}</el-button
            >
            <el-button
              v-if="generating"
              type="danger"
              icon="el-icon-close"
              @click="stopGeneration"
              plain
              >停止生成</el-button
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TinyEditor from "@/components/TinyEditor";
import {
  addZntXz as addDraft,
  updateZntXz as updateDraft,
  getZntXz as getDraft,
  listZntXz,
  downloadZntXzWord,
} from "@/api/gongwen/mydrafts";
import {
  downloadWordDocument,
  handleDownloadError,
  downloadWithFetch,
} from "@/utils/download";
import { getToken } from "@/utils/auth";
import {
  generateGongwen,
  generateDefaultGongwen,
  generateGongwenStream,
} from "@/api/gongwen/gongwen-api";

export default {
  name: "GongwenEditor",
  components: { TinyEditor },
  data() {
    return {
      minEditorHeight: window.innerHeight - 100,
      saving: false,
      autoSaving: false,
      autoSaveTimer: null,
      hasContentChanged: false,
      generating: false,
      openCollapse: [],
      activeCollapse: "requirements",
      activeCollapse2: "outline",
      activeCollapse3: "thinking",
      // 快捷类型相关
      selectedCategory: "法定公文",
      legalDocumentTypes: [
        "通知",
        "通告",
        "批复",
        "报告",
        "请示",
        "意见",
        "决议",
        "决定",
        "议案",
        "函",
        "通报",
        "公告",
        "纪要",
        "公报",
        "令",
      ],
      businessDocumentTypes: [
        "会议纪要",
        "方案",
        "工作总结",
        "工作报告",
        "制度",
        "整改报告",
        "讲话稿",
        "调研报告",
        "通报",
        "述职报告",
        "感谢信",
        "表扬信",
        "情况报告",
        "事件评价",
        "新闻稿",
        "考察报告",
        "日程议程",
        "学习体会",
        "请假条",
        "其他",
      ],
      // 流式生成相关状态
      showThinkingProcess: false,
      thinkingContent: "",
      generationProgress: 0,
      currentWordCount: 0,
      abortController: null,
      pendingContent: "",
      thinkingCompleted: false, // 标记思考过程是否已完成
      form: {
        id: undefined,
        title: "",
        type: "通知",
        minWordCount: 500,
        maxWordCount: 1000,
        content: "",
        summary: "",
        org: "",
        priority: "一般",
        effectDate: "",
        requirements: "",
        outline: "",
      },
    };
  },
  computed: {
    // 当前显示的类型列表
    currentTypes() {
      return this.selectedCategory === "法定公文"
        ? this.legalDocumentTypes
        : this.businessDocumentTypes;
    },
  },
  created() {
    // 支持从 params 或 query 中获取 id，确保兼容性
    const id = this.$route.params.id || this.$route.query.id;
    console.log("编辑器页面初始化 - 路由参数:", {
      params: this.$route.params,
      query: this.$route.query,
      id: id,
    });
    if (id) {
      console.log("准备加载稿件详情，ID:", id);
      this.load(id);
    } else {
      console.log("未找到稿件ID，创建新稿件");
    }


  },
  beforeDestroy() {
    // 取消正在进行的生成请求
    if (this.abortController) {
      this.abortController.abort();
    }
    // 清理自动保存定时器
    this.stopAutoSave();
  },
  methods: {
    // 处理大类切换
    onCategoryChange(category) {
      // 切换大类时，自动选择该类别的第一个类型
      const newTypes =
        category === "法定公文"
          ? this.legalDocumentTypes
          : this.businessDocumentTypes;

      // 如果当前选中的类型不在新类别中，则选择第一个
      if (!newTypes.includes(this.form.type)) {
        this.form.type = newTypes[0];
      }
    },

    // 根据类型设置对应的大类
    setSelectedCategoryByType(type) {
      if (this.legalDocumentTypes.includes(type)) {
        this.selectedCategory = "法定公文";
      } else if (this.businessDocumentTypes.includes(type)) {
        this.selectedCategory = "事务文书";
      } else {
        // 默认为法定公文
        this.selectedCategory = "法定公文";
      }
    },

    // 判断类型是否可点击
    isTypeClickable(type) {
      const clickableTypes = ["通知", "通告", "批复"];
      return clickableTypes.includes(type);
    },

    // 选择类型
    selectType(type) {
      if (this.isTypeClickable(type)) {
        this.form.type = type;
      }
    },

    async load(id) {
      try {
        console.log("开始调用 getZntXz 接口，ID:", id);
        const response = await getDraft(id);
        console.log("getZntXz 接口响应:", response);

        const data = response.data;
        console.log("解析的数据:", data);

        // 兼容不同字段命名
        this.form = {
          id: data.id || id,
          title: data.title || data.name || "",
          type: data.gwType || "通知",
          minWordCount: data.zsxzMin || 500,
          maxWordCount: data.zsxzMax || 1000,
          content: data.content || "",
          summary: data.summary || "",
          org: data.org || "",
          priority: data.priority || "一般",
          effectDate: data.effectDate || "",
          requirements: data.nryq || "",
          outline: data.gwdg || "",
        };

        // 根据类型设置对应的大类
        this.setSelectedCategoryByType(this.form.type);

        console.log("设置的表单数据:", this.form);
      } catch (error) {
        console.error("加载稿件详情失败:", error);
        this.msgError("加载稿件详情失败: " + (error.message || error));
      }
    },
    // 启动自动保存倒计时
    startAutoSaveCountdown() {
      // 清除之前的定时器
      this.stopAutoSave();

      // 5秒后自动保存
      this.autoSaveTimer = setTimeout(() => {
        this.autoSaveDraft();
      }, 5000);
    },
    // 停止自动保存
    stopAutoSave() {
      if (this.autoSaveTimer) {
        clearTimeout(this.autoSaveTimer);
        this.autoSaveTimer = null;
      }
    },
    // 自动保存方法
    async autoSaveDraft() {
      // 如果正在手动保存或生成中，或者没有内容变化，跳过自动保存
      if (this.saving || this.generating || !this.form.title || !this.hasContentChanged) {
        return;
      }

      this.autoSaving = true;
      try {
        await this.performSave(false); // false表示不显示成功消息
        this.hasContentChanged = false; // 保存后重置变化标记
      } catch (error) {
        console.error("自动保存失败:", error);
      } finally {
        this.autoSaving = false;
      }
    },
    // 提取保存逻辑为公共方法
    async performSave(showMessage = true) {
      const data = {
        ...this.form,
        nryq: this.form.requirements,
        gwType: this.form.type,
        gwdg: this.form.outline,
        zsxzMin: this.form.minWordCount,
        zsxzMax: this.form.maxWordCount,
      };

      if (this.form.id) {
        const res = await updateDraft(data);
        console.log("更新稿件响应:", res);
      } else {
        const res = await addDraft(data);
        console.log("创建稿件响应:", res);

        let newId = null;
        if (typeof res === "object" && res.data) {
          newId = res.data;
        } else if (typeof res === "string" || typeof res === "number") {
          newId = res;
        }

        if (newId) {
          this.form.id = newId;
          this.$router.replace({
            path: `/gongwen/editor/${newId}`,
          });
        }
      }

      if (showMessage) {
        this.msgSuccess("已保存");
      }

      // 保存成功后重置变化标记
      this.hasContentChanged = false;
    },
    async saveDraft() {
      if (!this.form.title) {
        this.msgError("请先填写标题");
        return;
      }
      this.saving = true;
      try {
        await this.performSave(true); // true表示显示成功消息
      } catch (error) {
        console.error("保存失败:", error);
        this.msgError("保存失败: " + (error.message || error));
      } finally {
        this.saving = false;
      }
    },
    backToList() {
      this.$router.push({ path: "/gongwen/mydrafts" });
    },
    async generateContent() {
      // 验证必填字段
      if (!this.form.title) {
        this.msgError("请先填写文章标题");
        return;
      }

      // 设置默认值
      const requirements =
        this.form.requirements ||
        "请根据实际情况编写内容，语言简洁、指令清晰，符合党政机关公文格式规范";
      const outline =
        this.form.outline ||
        "一、基本情况\n二、主要内容\n三、工作要求\n四、其他事项";
      const minWordCount = this.form.minWordCount || 500;
      const maxWordCount = this.form.maxWordCount || 1000;

      // 初始化流式生成状态
      this.generating = true;
      this.showThinkingProcess = true;
      this.thinkingContent = "";
      this.generationProgress = 0;
      this.currentWordCount = 0;
      this.thinkingCompleted = false; // 重置思考完成状态
      this.form.content = ""; // 清空编辑器内容

      // 展开思考过程面板
      this.activeCollapse3 = "thinking";

      // 滚动到思考过程区域
      this.$nextTick(() => {
        this.scrollRightPanelToBottom();
      });

      try {
        console.log("开始流式生成公文，参数:", {
          type: this.form.type,
          title: this.form.title,
          requirements,
          outline,
          min: minWordCount,
          max: maxWordCount,
        });

        // 创建 AbortController 用于取消请求
        this.abortController = new AbortController();

        // 将大纲按行分割为数组
        const outlineArray = outline.split("\n").filter((line) => line.trim());

        const params = {
          type: this.form.type,
          title: this.form.title,
          requirements,
          outline: outlineArray,
          min: minWordCount,
          max: maxWordCount,
        };

        const result = await generateGongwenStream(
          params,
          this.onProgress,
          this.onComplete,
          this.onError
        );

        if (!result.success && result.error) {
          this.msgError("公文生成失败：" + result.error.message);
        }
      } catch (error) {
        console.error("生成公文时发生错误:", error);
        this.msgError("生成失败：" + (error.message || "网络错误，请重试"));
        this.resetGenerationState();
      }
    },

    // 流式生成进度回调
    onProgress(deltaContent, fullContent) {
      // 调试：打印原始内容
      console.log("=== 流式内容调试 ===");
      console.log("deltaContent:", deltaContent);
      console.log("fullContent长度:", fullContent.length);
      console.log("fullContent前500字符:", fullContent.substring(0, 500));

      // 检查原始内容是否包含样式
      const hasStyles =
        fullContent.includes("style=") ||
        fullContent.includes("<span") ||
        fullContent.includes("<p");
      console.log("原始内容是否包含样式:", hasStyles);

      // 实时处理思考过程和正文内容
      let processedContent = fullContent;
      let currentThinking = "";

      // 检查是否有未完成的思考标签
      const openThinkIndex = processedContent.lastIndexOf("<think>");
      const closeThinkIndex = processedContent.lastIndexOf("</think>");

      if (openThinkIndex !== -1) {
        if (closeThinkIndex === -1 || openThinkIndex > closeThinkIndex) {
          // 思考过程正在进行中，提取当前思考内容
          currentThinking = processedContent.substring(openThinkIndex + 7); // 7 是 '<think>' 的长度
          this.thinkingContent = currentThinking;

          // 从正文中移除未完成的思考部分
          processedContent = processedContent.substring(0, openThinkIndex);
        } else {
          // 思考过程已完成，提取完整思考内容
          const thinkingMatch = processedContent.match(
            /<think>(.*?)<\/think>/s
          );
          if (thinkingMatch) {
            this.thinkingContent = thinkingMatch[1].trim();
            this.thinkingCompleted = true; // 标记思考过程已完成
          }

          // 从正文中移除所有思考过程
          processedContent = processedContent.replace(
            /<think>.*?<\/think>/gs,
            ""
          );
        }

        // 只在思考过程进行中才滚动右侧面板
        if (!this.thinkingCompleted) {
          this.$nextTick(() => {
            this.scrollRightPanelToBottom();
          });
        }
      }

      console.log(
        "移除思考过程后的内容:",
        processedContent.substring(0, 200) + "..."
      );

      // 清理markdown代码块标记
      processedContent = this.cleanMarkdownCodeBlocks(processedContent);
      console.log(
        "清理markdown后的内容:",
        processedContent.substring(0, 200) + "..."
      );

      // 保留HTML样式的处理 - 使用更安全的方法
      processedContent = this.preserveHtmlStylesSafe(processedContent);
      console.log(
        "保留样式后的内容:",
        processedContent.substring(0, 200) + "..."
      );

      // 存储待更新的内容
      this.pendingContent = processedContent.trim();

      // 调试：检查最终内容是否还有样式
      const finalHasStyles =
        this.pendingContent.includes("style=") ||
        this.pendingContent.includes("<span") ||
        this.pendingContent.includes("<p");
      console.log("最终内容是否包含样式:", finalHasStyles);
      console.log(
        "处理后的内容:",
        this.pendingContent.substring(0, 200) + "..."
      );

      // 计算字数时使用纯文本长度
      const textContent = processedContent.replace(/<[^>]*>/g, "").trim();
      this.currentWordCount = textContent.length;

      // 计算进度
      const targetWords = this.form.maxWordCount || 1000;
      this.generationProgress = Math.min(
        Math.round((this.currentWordCount / targetWords) * 100),
        95
      );

      // 立即更新编辑器内容以显示流式结果
      console.log(
        "设置编辑器内容:",
        this.pendingContent.substring(0, 200) + "..."
      );
      this.form.content = this.pendingContent;
    },

    // 保留HTML样式
    preserveHtmlStyles(content) {
      // 确保常见的HTML标签和样式被保留
      // 这个方法确保样式属性不会被意外移除

      console.log(
        "preserveHtmlStyles - 输入内容:",
        content.substring(0, 200) + "..."
      );

      // 如果内容为空或只有空白字符，直接返回
      if (!content || !content.trim()) {
        return content;
      }

      // 保护已有的HTML标签和样式，避免被意外修改
      let processedContent = content;

      // 1. 保护style属性，避免被破坏
      const styleMatches = [];
      processedContent = processedContent.replace(
        /style\s*=\s*"([^"]*)"/gi,
        (_, styles) => {
          const index = styleMatches.length;
          styleMatches.push(styles);
          return `__STYLE_PLACEHOLDER_${index}__`;
        }
      );

      // 2. 保护HTML标签
      const tagMatches = [];
      processedContent = processedContent.replace(
        /<([^>]+)>/g,
        (_, tagContent) => {
          const index = tagMatches.length;
          tagMatches.push(tagContent);
          return `__TAG_PLACEHOLDER_${index}__`;
        }
      );

      // 3. 恢复HTML标签
      processedContent = processedContent.replace(
        /__TAG_PLACEHOLDER_(\d+)__/g,
        (_, index) => {
          return `<${tagMatches[parseInt(index)]}>`;
        }
      );

      // 4. 恢复并优化style属性
      processedContent = processedContent.replace(
        /__STYLE_PLACEHOLDER_(\d+)__/g,
        (_, index) => {
          const styles = styleMatches[parseInt(index)];

          // 清理和标准化样式
          const cleanStyles = styles
            .split(";")
            .map((style) => style.trim())
            .filter((style) => style.length > 0)
            .map((style) => {
              // 确保样式格式正确
              if (
                style.includes("font-size") &&
                !style.includes("px") &&
                !style.includes("pt") &&
                !style.includes("em")
              ) {
                const sizeMatch = style.match(/font-size:\s*(\d+(?:\.\d+)?)/);
                if (sizeMatch) {
                  return style.replace(
                    /font-size:\s*\d+(?:\.\d+)?/,
                    `font-size: ${sizeMatch[1]}px`
                  );
                }
              }
              return style;
            })
            .join("; ");

          return `style="${cleanStyles}"`;
        }
      );

      // 5. 确保常见HTML实体被正确处理
      processedContent = processedContent.replace(/&nbsp;/g, " ");

      console.log(
        "preserveHtmlStyles - 输出内容:",
        processedContent.substring(0, 200) + "..."
      );

      return processedContent;
    },

    // 更安全的样式保留方法
    preserveHtmlStylesSafe(content) {
      if (!content || typeof content !== "string") {
        return content;
      }

      console.log(
        "preserveHtmlStylesSafe - 输入:",
        content.substring(0, 100) + "..."
      );

      // 简单但有效的方法：只做最基本的清理，保留所有HTML标签和样式
      let cleanedContent = content;

      // 只移除明显的非HTML内容
      // 1. 移除可能的BOM标记
      cleanedContent = cleanedContent.replace(/^\uFEFF/, "");

      // 2. 确保HTML实体正确
      cleanedContent = cleanedContent.replace(/&nbsp;/g, " ");

      // 3. 不做任何其他处理，保持原始HTML格式

      console.log(
        "preserveHtmlStylesSafe - 输出:",
        cleanedContent.substring(0, 100) + "..."
      );

      return cleanedContent;
    },

    // 清理markdown代码块标记
    cleanMarkdownCodeBlocks(content) {
      if (!content || typeof content !== "string") {
        return content;
      }

      console.log(
        "cleanMarkdownCodeBlocks - 输入:",
        content.substring(0, 100) + "..."
      );

      // 只在确实是markdown代码块的情况下才清理
      let cleanedContent = content;

      // 移除开头的 ```html 标记（只在行首）
      cleanedContent = cleanedContent.replace(/^```html\s*/i, "");

      // 移除结尾的 ``` 标记（只在行尾）
      cleanedContent = cleanedContent.replace(/\s*```\s*$/i, "");

      // 移除其他可能的markdown代码块标记，但要小心不要误删HTML内容
      // 只移除明显的markdown代码块标记
      cleanedContent = cleanedContent.replace(/^```[a-zA-Z]*\s*/gm, "");
      cleanedContent = cleanedContent.replace(/\s*```\s*$/gm, "");

      console.log(
        "cleanMarkdownCodeBlocks - 输出:",
        cleanedContent.substring(0, 100) + "..."
      );

      return cleanedContent;
    },

    // 流式生成完成回调
    onComplete(finalContent) {
      console.log(
        "生成完成 - 原始内容:",
        finalContent.substring(0, 500) + "..."
      );

      // 清理思考过程标记，只保留正文内容
      let cleanContent = finalContent
        .replace(/<think>.*?<\/think>/gs, "")
        .trim();

      // 清理markdown代码块标记
      cleanContent = this.cleanMarkdownCodeBlocks(cleanContent);

      // 保留HTML样式 - 使用更安全的方法
      cleanContent = this.preserveHtmlStylesSafe(cleanContent);

      console.log(
        "生成完成 - 处理后内容:",
        cleanContent.substring(0, 500) + "..."
      );

      // 立即更新编辑器内容
      this.form.content = cleanContent;
      this.pendingContent = cleanContent;

      // 计算字数时使用纯文本长度
      const textContent = cleanContent.replace(/<[^>]*>/g, "").trim();
      this.currentWordCount = textContent.length;
      this.generationProgress = 100;

      // 设置摘要
      if (!this.form.summary) {
        this.form.summary = `${this.form.type}：${this.form.title}`;
      }

      // this.msgSuccess("公文生成完成！");
      this.resetGenerationState();

      // 生成完成后不再滚动右侧面板，让用户保持当前查看位置
    },

    // 流式生成错误回调
    onError(error) {
      console.error("流式生成过程中发生错误:", error);
      this.msgError("生成过程中发生错误：" + error.message);
      this.resetGenerationState();
    },

    // 停止生成
    stopGeneration() {
      if (this.abortController) {
        this.abortController.abort();
        this.msgInfo("已停止生成");
        this.resetGenerationState();
      }
    },

    // 重置生成状态
    resetGenerationState() {
      this.generating = false;
      this.abortController = null;
      this.generationProgress = 0;
      this.thinkingCompleted = false; // 重置思考完成状态
      // this.showThinkingProcess = false; // 隐藏思考过程区域
      this.pendingContent = "";
    },
    // 最小字数变化时的验证
    onMinWordCountChange(value) {
      if (value && this.form.maxWordCount && value >= this.form.maxWordCount) {
        this.form.maxWordCount = value + 100;
        this.$message.warning("已自动调整最大字数，确保范围合理");
      }
    },
    // 最大字数变化时的验证
    onMaxWordCountChange(value) {
      if (value && this.form.minWordCount && value <= this.form.minWordCount) {
        this.form.minWordCount = Math.max(50, value - 100);
        this.$message.warning("已自动调整最小字数，确保范围合理");
      }
    },

    // 处理粘贴图片被阻止的事件
    onPasteImageBlocked(message) {
      this.msgError(message || "禁止粘贴图片");
    },

    // 处理TinyEditor导出Word事件
    handleExportWord(data) {
      console.log("TinyEditor导出Word:", data);
      if (data.success) {
        this.msgSuccess(`Word文档导出成功: ${data.filename}`);
      } else {
        this.msgError("Word文档导出失败: " + (data.error || "未知错误"));
      }
    },

    // 处理内容变化事件
    handleContentChange(data) {
      console.log("TinyEditor内容变化:", data);

      // 标记内容已变化
      this.hasContentChanged = true;

      // 启动5秒倒计时自动保存
      this.startAutoSaveCountdown();

      // 可以在这里添加字数统计等功能
    },

    // 滚动右侧面板到底部，确保思考过程始终可见
    scrollRightPanelToBottom() {
      if (this.$refs.rightPanel) {
        // 找到右侧面板内的滚动容器
        const scrollContainer = this.$refs.rightPanel
        if (scrollContainer) {
          scrollContainer.scrollTop = scrollContainer.scrollHeight;
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.gw-editor-page {
  padding: 12px 16px 20px;
}
.page-header {
  display: grid;
  grid-template-columns: auto 1fr auto;
  grid-column-gap: 12px;
  align-items: center;
  margin-bottom: 10px;
  .title-display {
    font-size: 18px;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .header-save {
    justify-self: end;
  }
}
.page-body {
  display: grid;
  grid-template-columns: 1400px auto;
  grid-column-gap: 16px;
  align-items: start;
}
.left {
  background: #fff;
  //border: 1px solid #ebeef5;
  // border-radius: 6px;
  overflow: hidden;
  /* 设置A4纸张宽度 (210mm ≈ 794px at 96DPI) */
  width: 1400px;
}
.right {
  position: relative;
  flex: 1;
  flex-shrink: 0;
  height: calc(100vh - 100px);

  .panel.document-settings {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    padding: 20px;
    border: 1px solid #ebeef5;
    /* 添加以下关键属性 */
    min-height: 0; /* 关键：允许flex子元素收缩 */
    flex: 1 1 0; /* 明确指定flex属性 */

    :deep(.el-card__body) {
      flex: 1;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      /* 添加以下关键属性 */
      min-height: 0; /* 关键：允许flex子元素收缩 */
      flex: 1 1 0; /* 明确指定flex属性 */
    }
  }
}
.panel {
  margin-bottom: 12px;
}
.panel-title {
  font-weight: 600;
}
/* 快捷类型样式 */
.quick-types {
  .type-category {
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;

    :deep(.el-radio-group) {
      display: flex;
      gap: 16px;
    }

    :deep(.el-radio) {
      margin-right: 0;
      font-weight: 500;

      .el-radio__label {
        font-size: 14px;
        color: #606266;
      }

      &.is-checked .el-radio__label {
        color: #409eff;
        font-weight: 600;
      }
    }
  }

  .type-buttons {
    padding-top: 2px;

    .type-grid {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 8px;

      .type-btn {
        width: 100%;
        font-size: 12px;
        padding: 8px 4px;
        border-radius: 4px;
        transition: all 0.3s;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        // 法定公文样式
        &.select1 {
          border-color: #409eff;
          color: #409eff;
          background-color: #fff;

          &:hover:not(:disabled) {
            background-color: #ecf5ff;
            border-color: #66b1ff;
          }

          &.is-active {
            background-color: #409eff;
            border-color: #409eff;
            color: #fff;

            &:hover:not(:disabled) {
              background-color: #337ecc;
              border-color: #337ecc;
            }
          }

          // 禁用状态
          &:disabled {
            background-color: #f5f7fa;
            border-color: #e4e7ed;
            color: #c0c4cc;
            cursor: not-allowed;

            &.is-active {
              background-color: #f5f7fa;
              border-color: #e4e7ed;
              color: #c0c4cc;
            }
          }
        }
      }
    }
  }
}
.panel-collapse {
  margin-bottom: 84px;
}
.sticky-actions {
  margin-top: auto;
  padding-top: 12px;
  display: flex;
  justify-content: space-between;
  background: #fff;
  border-top: 1px solid #ebeef5;
}

/* 让编辑器工具栏跟随容器宽度 */
.left :deep(.ql-toolbar) {
  border-bottom: 1px solid #ebeef5;
}
.left :deep(.ql-container) {
  min-height: 520px;
}

/* 文稿设置样式 */
.document-settings {
  .panel-title {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    color: #303133;
  }

  .setting-item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .setting-label {
    font-size: 13px;
    color: #606266;
    margin-bottom: 8px;
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 6px;
    .required {
      color: #f56c6c;
    }
  }

  .document-title {
    font-size: 14px;
    color: #303133;
    background: #f5f7fa;
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
  }

  .word-count-range {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;

    .range-separator {
      font-size: 13px;
      color: #606266;
      margin: 0 4px;
    }

    .word-unit {
      font-size: 13px;
      color: #606266;
      margin-left: 4px;
    }

    :deep(.el-input-number) {
      .el-input__inner {
        text-align: left;
      }
    }
  }

  .collapse-title {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    color: #606266;
  }

  .requirements-content,
  .outline-content,
  .thinking-content {
    padding-top: 8px;
  }

  .thinking-display {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 12px;
    min-height: 80px;
    font-family: "Courier New", monospace;
    font-size: 12px;
    line-height: 1.5;
    white-space: pre-wrap;
    word-wrap: break-word;
    scroll-behavior: smooth;
  }

  .thinking-display.streaming {
    border-left: 3px solid #409eff;
    animation: pulse 1.5s infinite;
  }

  @keyframes pulse {
    0% {
      border-left-color: #409eff;
    }
    50% {
      border-left-color: #67c23a;
    }
    100% {
      border-left-color: #409eff;
    }
  }

  .thinking-placeholder {
    color: #909399;
    font-style: italic;
  }

  .thinking-text {
    color: #606266;
  }

  .generation-progress {
    margin-top: 12px;
    padding-top: 8px;
    border-top: 1px solid #ebeef5;
  }

  .progress-text {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    text-align: center;
  }

  :deep(.el-collapse) {
    border: none;
  }

  :deep(.el-collapse-item) {
    border: 1px solid #ebeef5;
    border-radius: 6px;
    overflow: hidden;
    background: #fff;
  }

  :deep(.el-collapse-item__header) {
    background: #f7f9fc;
    border-bottom: 1px solid #ebeef5;
    padding: 8px 12px;
    height: 36px;
    line-height: 20px;
    font-size: 13px;
  }

  :deep(.el-collapse-item__content) {
    padding: 12px;
  }

  :deep(.el-collapse-item__wrap) {
    border: none;
  }
}
</style>
<style>
.el-button + .el-button {
  margin-left: 0;
}
</style>
