import request from '@/utils/request'

// 查询视频标签表列表
export function listTreeindex(query) {
  return request({
    url: '/screen/index/treelist',
    method: 'get',
    params: query,
  })
}

// 查询指标管理列表
export function listIndex(query) {
  return request({
    url: '/screen/info/list',
    // url: '/screen/index/list',
    method: 'get',
    params: query,
  })
}

// 查询指标管理列表
export function listCardIndex(query) {
  return request({
    url: '/screen/info/cardlist',
    method: 'get',
    params: query,
  })
}

// 查询指标管理列表
export function listAllIndex(query) {
  return request({
    url: '/screen/index/treelist',
    method: 'get',
    params: query,
  })
}

// 查询指标管理详细
export function getIndex(indexId) {
  return request({
    url: '/screen/index/' + indexId,
    method: 'get',
  })
}
// 查询指标管理详细
export function getIndicator(indexId) {
  return request({
    url: '/screen/info/' + indexId,
    method: 'get',
  })
}

// 新增指标管理
export function addIndex(data) {
  return request({
    url: '/screen/index',
    method: 'post',
    data: data,
  })
}

export function addIndicator(data) {
  return request({
    url: '/screen/info',
    method: 'post',
    data: data,
  })
}

// 修改指标管理
export function updateIndex(data) {
  return request({
    url: '/screen/index',
    method: 'put',
    data: data,
  })
}
// 修改指标管理
export function updateIndicator(data) {
  return request({
    url: '/screen/info',
    method: 'put',
    data: data,
  })
}
export function updateIndicatorAttr(data) {
  return request({
    url: '/screen/attribute',
    method: 'put',
    data: data,
  })
}

// 删除指标管理
export function delIndex(indexId) {
  return request({
    url: '/screen/index/' + indexId,
    method: 'delete',
  })
}

export function delIndicator(indexId) {
  return request({
    url: '/screen/info/' + indexId,
    method: 'delete',
  })
}

// 导出指标管理
export function exportIndex(query) {
  return request({
    url: '/screen/category/export',
    method: 'get',
    params: query,
  })
}

export function exportIndicator(query) {
  return request({
    url: '/screen/info/export',
    method: 'get',
    params: query,
  })
}

// 查询指标下拉树结构
export function treeselect(query) {
  return request({
    // url: '/screen/index/treeselectcount',
    url: '/screen/info/treelist',
    method: 'get',
    params: query,
  })
}

// 查询指标项
export function getIndexItemList(query) {
  return request({
    url: '/screen/index/getIndexItemList',
    method: 'get',
    params: query,
  })
}
// 查询指标项
export function treeselectAllcount() {
  return request({
    url: '/screen/index/treeselectAllcount',
    method: 'get',
  })
}
export function treeItemCount() {
  return request({
    url: '/screen/index/treeItemCount',
    method: 'get',
  })
}

export function getSourceDepts() {
  return request({
    url: '/screen/info/sourceDepts',
    method: 'get',
  })
}

// 父级指标+指标显示名称列表
export function getIndicatorList(query) {
  return request({
    url: '/screen/relation/list',
    method: 'get',
    params: query,
  })
}

// 新增父级指标+指标显示名称
export function addIndicatorItem(data) {
  return request({
    url: '/screen/relation',
    method: 'post',
    data: data,
  })
}

// 删除父级指标+指标显示名称
export function removeIndicatorItem(data) {
  return request({
    url: '/screen/relation/remove',
    method: 'post',
    data: data,
  })
}

/**
 * 查询指标标签列表
 * @param {*} params
 * @returns
 */
export function getLabelList(params) {
  return request({
    url: '/screen/info/listLabel',
    method: 'get',
    params,
  })
}

/**
 * 指标项点击标签配置->标签grid表格列表接口开发
 * @param {*} indicatorId
 * @returns
 */
export function getLabelsById(params) {
  return request({
    url: '/screen/label/list',
    method: 'get',
    params,
  })
}

/**
 * 指标项点击标签配置->标签添加接口开发
 * @data {*} {indicatorIdl,labelName}
 * @returns
 */
export function addLabel(data) {
  return request({
    url: '/screen/label',
    method: 'post',
    data,
  })
}

/**
 * 指标项点击标签配置->标签删除接口开发
 * @param {*} id
 * @returns
 */
export function deleteLabelById(id) {
  return request({
    url: '/screen/label/' + id,
    method: 'delete',
  })
}

/**
 * 按标签设置指标页面-列表
 * @param {*} params
 * @returns
 */
export function getAllTagList(params) {
  return request({
    url: '/screen/label/labelList',
    method: 'get',
    params,
  })
}

/**
 * 按标签设置指标页面批量绑定
 * @param {*} data
 * @returns
 */
export function batchLabelBinding(data) {
  return request({
    url: '/screen/label/batchBading',
    method: 'post',
    data,
  })
}

/**
 * 按标签设置指标页面批量解绑
 * @param {*} data
 * @returns
 */
export function batchLabelRemove(data) {
  return request({
    url: '/screen/label/delBatchBading',
    method: 'post',
    data,
  })
}

/**
 * 标签设置指标页面->类别下拉
 * @returns
 */
export function getCategoryList() {
  return request({
    url: '/screen/label/parentCategroySel',
    method: 'get',
  })
}

/**
 * 标签设置指标页面->类别查1级

 * @param {*} params
 * @returns
 */
export function getFirstIndicators(params) {
  return request({
    url: '/screen/label/oneIndicatorSel',
    method: 'get',
    params,
  })
}

/**
 * 标签设置指标页面->1级查2级
 * @param {*} params
 * @returns
 */
export function getSecondIndicators(params) {
  return request({
    url: '/screen/label/twoIndicatorSel',
    method: 'get',
    params,
  })
}

/**
 * 标签设置指标页面->标签快捷显示
 * @param {*} params
 * @returns
 */
export function getLabelNames(params) {
  return request({
    url: '/screen/label/getLabelName',
    method: 'get',
    params,
  })
}
