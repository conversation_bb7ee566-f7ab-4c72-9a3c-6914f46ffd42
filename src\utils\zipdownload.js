import axios from 'axios'
import { getToken } from '@/utils/auth'

const mimeMap = {
  xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  zip: 'application/zip'
}

const baseUrl = process.env.VUE_APP_BASE_API
export function downLoadZip(str, filename) {
  var url = baseUrl + str
  axios({
    method: 'get',
    url: url,
    responseType: 'blob',
    headers: { 'Authorization': 'Bearer ' + getToken() }
  }).then(res => {
    resolveBlob(res, mimeMap.zip)
  })
}
/**
 * 解析blob响应内容并下载
 * @param {*} res blob响应内容
 * @param {String} mimeType MIME类型
 */
export function resolveBlob(res, mimeType) {
  const aLink = document.createElement('a')
  var blob = new Blob([res.data], { type: mimeType })
  // //从response的headers中获取filename, 后端response.setHeader("Content-disposition", "attachment; filename=xxxx.docx") 设置的文件名;
  var patt = new RegExp('filename=([^;]+\\.[^\\.;]+);*')
  var contentDisposition = decodeURI(res.headers['content-disposition'])
  var result = patt.exec(contentDisposition)
  var fileName = result[1]
  fileName = fileName.replace(/\"/g, '')
  aLink.href = URL.createObjectURL(blob)
  aLink.setAttribute('download', fileName) // 设置下载文件名称
  document.body.appendChild(aLink)
  aLink.click()
  document.body.appendChild(aLink)
}

export function downloadFile(res, fileName) {
  const aLink = document.createElement('a')
  const blob = new Blob([res])
  // //从response的headers中获取filename, 后端response.setHeader("Content-disposition", "attachment; filename=xxxx.docx") 设置的文件名;
  // console.log(res)
  aLink.href = URL.createObjectURL(blob)
  aLink.setAttribute('download', fileName) // 设置下载文件名称
  document.body.appendChild(aLink)
  aLink.click()
  document.body.removeChild(aLink)
}

export function downloadExcelFile(fileName) {
  // 确保文件名安全且符合预期
  const sanitizedFileName = encodeURIComponent(fileName);

  // 构建文件的URL，假设Excel文件存储在public目录下
  // 注意：Vue CLI 3及更高版本推荐将静态资源放在public文件夹
  const fileUrl = `${process.env.BASE_URL}Excels/${sanitizedFileName}`;

  // 创建隐藏的可下载链接
  const link = document.createElement('a');
  link.href = fileUrl;
  link.target = '_blank';
  link.download = fileName; // 设置下载的文件名
  link.style.display = 'none';

  // 将链接附加到文档中并模拟点击
  document.body.appendChild(link);
  link.click();

  // 清理：下载后移除链接
  document.body.removeChild(link);
}
