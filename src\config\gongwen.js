/**
 * 公文生成API配置
 * 根据不同环境自动选择对应的API配置
 */

// 从环境变量获取配置
const getGongwenConfig = () => {
  const config = {
    url: process.env.VUE_APP_GONGWEN_API_URL,
    apiKey: process.env.VUE_APP_GONGWEN_API_KEY,
    timeout: 100000, // 100秒
  };

  // 验证配置是否完整
  if (!config.url || !config.apiKey) {
    console.warn('公文生成API配置不完整，请检查环境变量：');
    console.warn('VUE_APP_GONGWEN_API_URL:', config.url);
    console.warn('VUE_APP_GONGWEN_API_KEY:', config.apiKey ? '已配置' : '未配置');
  }

  return config;
};

// 获取当前环境信息
const getEnvironmentInfo = () => {
  const env = process.env.NODE_ENV;
  const envType = process.env.ENV;
  
  return {
    nodeEnv: env,
    envType: envType,
    isDevelopment: env === 'development',
    isProduction: env === 'production',
    isTest: envType === 'test',
  };
};

// 导出配置
export const GONGWEN_CONFIG = getGongwenConfig();
export const ENV_INFO = getEnvironmentInfo();

// 默认导出
export default {
  config: GONGWEN_CONFIG,
  envInfo: ENV_INFO,
  getConfig: getGongwenConfig,
  getEnvInfo: getEnvironmentInfo,
};
