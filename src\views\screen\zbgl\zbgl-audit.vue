<template>
  <div class="app-container">
    <el-form :inline="true" :model="dataForm" ref="dataForm" v-show="showSearch" @keyup.enter.native="getDataList()">
      <el-form-item prop="bm">
        <el-input v-model="dataForm.bm" placeholder="指标编码" clearable></el-input>
      </el-form-item>
      <!-- <el-form-item prop="mch1">
        <el-cascader v-model="dataForm.mch1"
                     :show-all-levels="true"
                     placeholder="所属页面"
                     :options="ymzts"
                     :props="{children: 'children', label: 'dataName', value: 'dataCode', checkStrictly:true, multiple: false}"
                     clearable
                     filterable
        />
      </el-form-item> -->
      <el-form-item prop="mch2">
        <el-input v-model="dataForm.mch2" placeholder="指标概述" clearable></el-input>
      </el-form-item>
      <el-form-item prop="jb">
        <el-input v-model="dataForm.jb" placeholder="sql脚本" clearable></el-input>
      </el-form-item>
      <el-form-item prop="sjly">
        <el-select v-model="dataForm.sjly" placeholder="数据来源">
          <el-option :label="'全部'" :value="''"></el-option>
          <el-option v-for="item in this.sjlys" :label="item.dictLabel" :key="item.dictLabel" :value="item.dictValue"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table
      :data="dataList"
      v-loading="dataListLoading"
      style="width: 100%;">
      <el-table-column
        prop="bm"
        header-align="center"
        align="center"
        width="150"
        label="指标编码">
      </el-table-column>
      <!-- <el-table-column
        prop="mch1"
        header-align="center"
        align="center"
        label="所属页面">
      </el-table-column> -->
      <el-table-column
        prop="mch2"
        header-align="center"
        align="center"
        label="指标概述">
      </el-table-column>
      <el-table-column
        prop="sjly"
        header-align="center"
        align="center"
        width="220"
        label="数据来源">
      </el-table-column>
      <!-- <el-table-column
        prop="status"
        header-align="center"
        align="center"
        width="200"
        label="状态">
        <template slot-scope="scope">
                  <el-tag v-if="scope.row.status === 0" >正常</el-tag>
                  <el-tag v-else-if="scope.row.status === 1" >停止</el-tag>
                </template>
      </el-table-column> -->
      <el-table-column
        prop="dataStatus"
        header-align="center"
        align="center"
        label="数据状态">
        <template slot-scope="scope">
          <span v-if="scope.row.dataStatus == 1">在线数据</span>
          <span v-else-if="scope.row.dataStatus == 2">离线数据</span>
          <span v-else-if="scope.row.dataStatus == 3">模拟数据</span>
          <span v-else-if="scope.row.dataStatus == 4">多种数据</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="isAudit"
        header-align="center"
        align="center"
        label="审核状态">
        <template slot-scope="scope">
          <span v-if="scope.row.isAudit == 0">待审核</span>
          <span v-else-if="scope.row.isAudit == 1">审核通过</span>
          <span v-else-if="scope.row.isAudit == 9">审核不通过</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="bz"
        header-align="center"
        align="center"
        width="300"
        label="备注">
      </el-table-column>
      <el-table-column
        header-align="center"
        align="center"
        width="240"
        label="操作">
        <template slot-scope="scope">
          <el-button v-hasPermi="['screen:zbgl:edit']" type="text" size="small"
                     @click="addOrUpdateHandle(scope.row.id,'cat')">查看
          </el-button>
          <el-button type="text" size="small"
                     @click="examine(scope.row.bm)">审核记录
          </el-button>
          <el-button type="text" size="small"
                     @click="pass(scope.row.id)">通过
          </el-button>
          <el-button type="text" size="small"
                     @click="noPass(scope.row.id)">不通过
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="pageNum"
      :limit.sync="pageSize"
      @pagination="getDataList"
    />
    <!-- 弹窗, 新增 / 修改-->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <examine-list v-if="examineVisible" ref="examineList" @refreshDataList="getDataList"></examine-list>
    <audit-refuse v-if="auditRefuseVisible" ref="auditRefuse" @refreshDataList="getDataList"></audit-refuse>
  </div>
</template>

<script>
  import { findZbDataList, getDpztList, adopt, auditList } from "@/api/screen/zbgl"
  import AddOrUpdatezb from './zbgl-addOrUpdate.vue'
  import examineList from './zbgl-examine-list.vue'
  import auditRefuse from './zbgl-audit-refuse.vue'
  import {Loading} from 'element-ui'
  import {listToTree} from "@/utils/Tree";

  export default {
    data () {
      return {
        dataForm: {
          bm: '',
          mch1: '',
          mch2: '',
          sjly: '',
          jb: '',
          yxtz:0
        },
        sjlys: [{
          dictLabel: '',
          dictValue: ''
        }],
        ymzts: [{
          dataName: '',
          dataCode: ''
        }],

        // 显示搜索条件
        showSearch: true,
        dataList: [],
        total: 0,
        pageNum: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: [],
        permission: [],
        permissionVisible: {
          add: false,
          update: false,
          delete: false
        },
        addOrUpdateVisible: false,
        examineVisible: false,
        auditRefuseVisible: false,
        statuList:[
          {value:'1', label:"在线数据" },
          {value:'2', label:"离线数据" },
          {value:'3', label:"模拟数据" },
          {value:'4', label:"多种数据" }
        ],
        auditList:[
          {value:'0', label:"待审核" },
          {value:'1', label:"审核通过" },
          {value:'9', label:"审核不通过" }
        ],
      }
    },
    components: {
      addOrUpdate: AddOrUpdatezb,
      examineList: examineList,
      auditRefuse: auditRefuse
    },
    mounted () {
      this.getDpztList()
    },
    created() {
      this.getDataList();
      this.getDicts("screen_dataSource").then(response => {
        this.sjlys = response.data;
      });

    },

    methods: {
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        auditList({
          'pageNum': this.pageNum,
          'pageSize': this.pageSize,
          'bm': this.dataForm.bm,
          'mch1': this.dataForm.mch1,
          'mch2': this.dataForm.mch2,
          'sjly': this.dataForm.sjly,
          'jb': this.dataForm.jb
        }).then(response => {
          this.dataList = response.rows;
          this.total = response.total;
          this.dataListLoading = false;
        });
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.getDataList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("dataForm");
        this.handleQuery();
      },

      // 查看子页面函数
      addOrUpdateHandle (id, subType) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id, subType)
        })
      },

      // 审核记录
      examine(bm){
        this.examineVisible = true
        this.$nextTick(() => {
          this.$refs.examineList.init(bm)
        })
      },

      //通过
      pass(id){
        this.$confirm(`确定进行[通过]操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          adopt(id).then(res => {
            this.getDataList();
            this.msgSuccess("通过成功");
          });
        }).catch(() => {})
      },

      //不通过
      noPass(id){
        this.auditRefuseVisible = true
        this.$nextTick(() => {
          this.$refs.auditRefuse.init(id)
        })
      },

      // 获取页面专题
      async getDpztList () {
         await getDpztList({}).then(response => {
          this.ymzts = listToTree('id','parentCode',response.data);
        });
      }
    }
  }
</script>
