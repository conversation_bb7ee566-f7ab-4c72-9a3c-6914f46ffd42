<template>
  <el-dialog
    :title="'返回结果'"
    :modal-append-to-body="false"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="50%">
    <el-form :model="dataForm" ref="dataForm"
             label-width="15%">
      <span style="font-size: 16px;" v-html="'执行时长: '+dataForm.totalTimes+' ms'"></span>
      <el-input type="textarea" v-model="dataForm.tsjg" :autosize="{ minRows: 6, maxRows: 20}"></el-input>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">返回</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          totalTimes: '',
          tsjg: ''
        }
      }
    },
    created () {
    },
    methods: {
      init (totalTimes, tsjg) {
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          this.dataForm.totalTimes = totalTimes
          this.dataForm.tsjg = JSON.stringify(tsjg, null, '\t')
        })
      }
    }
  }
</script>
