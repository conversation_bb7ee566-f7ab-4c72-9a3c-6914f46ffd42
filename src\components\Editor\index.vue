<template>
  <div class="editor" ref="editor" :style="styles"></div>
</template>

<script>
import Quill from "quill";
import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";
import "quill/dist/quill.bubble.css";
// 引入自定义字体样式
import "../../assets/css/quillEditor.css";
// 导入字号映射
import { fontSizeWordMap, getQuillSizeWhitelist } from "../../utils/fontSizeMap";
// 导入html-to-docx用于Word导出
import HTMLtoDOCX from "html-to-docx";
import { saveAs } from "file-saver";

// 自定义字体列表
const fonts = [
  "SimSun",
  "SimHei",
  "Microsoft-YaHei",
  "KaiTi_GB2312",
  "FangSong_GB2312",
  "FangZheng_XiaoBiaoSong",
];

// 自定义字体样式类名


// 自定义字号列表 - 使用Word字号
const sizes = getQuillSizeWhitelist();

// 注册字体到Quill
const Font = Quill.import("formats/font");
Font.whitelist = fonts;
Quill.register(Font, true);

// 注册字号到Quill
const Size = Quill.import("formats/size");
Size.whitelist = sizes;
Quill.register(Size, true);

// 注册颜色格式
const Color = Quill.import("formats/color");
const Background = Quill.import("formats/background");
Quill.register(Color, true);
Quill.register(Background, true);

// 注册对齐格式
const Align = Quill.import("formats/align");
Quill.register(Align, true);

export default {
  name: "Editor",
  props: {
    /* 编辑器的内容 */
    value: {
      type: String,
      default: "",
    },
    /* 高度 */
    height: {
      type: Number,
      default: null,
    },
    /* 最小高度 */
    minHeight: {
      type: Number,
      default: null,
    },
    /* 是否显示导出按钮 */
    showExport: {
      type: Boolean,
      default: true,
    },
    /* 导出文件名 */
    exportFilename: {
      type: String,
      default: "文档.docx",
    },
    /* 是否保持原始HTML格式，不经过Quill处理 */
    preserveRawHtml: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      Quill: null,
      currentValue: "",
      options: {
        theme: "snow",
        bounds: document.body,
        debug: "warn",
        formats: [
          // 允许的格式列表，确保所有样式都被支持
          'bold', 'italic', 'underline', 'strike',
          'color', 'background',
          'font', 'size',
          'header', 'blockquote', 'code-block',
          'list', 'bullet', 'indent',
          'align', 'direction',
          'link', 'image', 'video'
        ],
        modules: {
          // 工具栏配置
          toolbar: [
            ["bold", "italic", "underline", "strike"], // 加粗 斜体 下划线 删除线
            ["blockquote", "code-block"], // 引用  代码块
            [{ list: "ordered" }, { list: "bullet" }], // 有序、无序列表
            [{ indent: "-1" }, { indent: "+1" }], // 缩进
            [{ font: fonts }], // 字体选择
            [{ size: sizes }], // 字体大小
            [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题
            [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
            [{ align: [] }], // 对齐方式
            // ["clean"],                                       // 清除文本格式
            // ["link", "image", "video"]                       // 链接、图片、视频
          ],
          // 配置剪贴板模块以保留文字样式但禁止粘贴base64图片
          clipboard: {
            // 粘贴版，处理粘贴时候的自带样式
            matchers: [
              [Node.ELEMENT_NODE, this.handleCustomMatcher],
              // 保留更多HTML样式的匹配器
              ['SPAN', this.handleSpanMatcher],
              ['P', this.handleParagraphMatcher],
              ['DIV', this.handleDivMatcher],
              ['STRONG', this.handleStrongMatcher],
              ['B', this.handleBoldMatcher],
              ['EM', this.handleEmMatcher],
              ['I', this.handleItalicMatcher],
              ['U', this.handleUnderlineMatcher]
            ],
            // 保持更多的HTML属性
            keepSelection: true,
            matchVisual: false
          }
        },
        placeholder: "请输入内容",
        readOnly: false,
      },
    };
  },
  computed: {
    styles() {
      let style = {};
      if (this.minHeight) {
        style.minHeight = `${this.minHeight}px`;
      }
      if (this.height) {
        style.height = `${this.height}px`;
      }
      return style;
    },
  },
  watch: {
    value: {
      handler(val) {
        if (val !== this.currentValue) {
          this.currentValue = val === null ? "" : val;
          if (this.Quill) {
            // 使用更安全的方式设置内容
            this.setEditorContent(this.currentValue);
          }
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.init();
  },
  beforeDestroy() {
    this.Quill = null;
  },
  methods: {
    init() {
      const editor = this.$refs.editor;

      this.Quill = new Quill(editor, this.options);

      // 设置初始内容
      if (this.currentValue) {
        this.setEditorContent(this.currentValue);
      }

      // 如果需要显示导出按钮，手动添加到工具栏
      if (this.showExport) {
        this.addExportButton();
      }

      // 设置事件处理器
      this.setupTextChangeHandler();

      this.Quill.on("selection-change", (range, oldRange, source) => {
        this.$emit("on-selection-change", range, oldRange, source);
      });
      this.Quill.on("editor-change", (eventName, ...args) => {
        this.$emit("on-editor-change", eventName, ...args);
      });
    },

    // 设置文本变化处理器
    setupTextChangeHandler() {
      this.Quill.on("text-change", (delta, oldDelta, source) => {
        const html = this.$refs.editor.children[0].innerHTML;
        const text = this.Quill.getText();
        const quill = this.Quill;
        this.currentValue = html;
        this.$emit("input", html);
        this.$emit("on-change", { html, text, quill });
      });
      this.Quill.on("text-change", (delta, oldDelta, source) => {
        this.$emit("on-text-change", delta, oldDelta, source);
      });
    },

    // 安全地设置编辑器内容
    setEditorContent(htmlContent) {
      if (!this.Quill || !htmlContent) {
        return;
      }

      // 如果启用了preserveRawHtml，直接设置innerHTML，不经过Quill处理
      if (this.preserveRawHtml) {
        console.log('使用原始HTML模式设置内容:', htmlContent.substring(0, 200) + '...');
        const editor = this.$refs.editor.querySelector('.ql-editor');
        if (editor) {
          // 直接设置HTML内容，保持原始格式
          editor.innerHTML = htmlContent;

          // 更新当前值
          this.currentValue = htmlContent;

          // 手动触发Vue的input事件
          this.$emit("input", htmlContent);

          // 触发change事件，提供完整的上下文
          this.$emit("on-change", {
            html: htmlContent,
            text: editor.textContent || editor.innerText || '',
            quill: this.Quill
          });

          console.log('原始HTML模式 - 内容已设置，HTML长度:', htmlContent.length);
          console.log('编辑器当前innerHTML:', editor.innerHTML.substring(0, 100) + '...');
        }
        return;
      }

      try {
        // 预处理HTML内容，确保样式能被正确解析
        const processedHtml = this.preprocessHtmlContent(htmlContent);

        // 方法1：使用clipboard模块的dangerouslyPasteHTML，保留更多HTML格式
        if (this.Quill.clipboard && this.Quill.clipboard.dangerouslyPasteHTML) {
          // 清空编辑器内容
          this.Quill.setContents([]);
          // 插入新内容
          this.Quill.clipboard.dangerouslyPasteHTML(processedHtml);
        } else {
          // 方法2：创建临时DOM元素，然后使用clipboard.convert
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = processedHtml;

          // 使用clipboard模块转换HTML为Delta格式
          const delta = this.Quill.clipboard.convert(tempDiv);
          this.Quill.setContents(delta);
        }
      } catch (error) {
        console.warn('使用高级方法设置内容失败，回退到pasteHTML:', error);
        try {
          // 回退到原始方法
          this.Quill.pasteHTML(htmlContent);
        } catch (fallbackError) {
          console.error('所有设置内容的方法都失败了:', fallbackError);
          // 最后的回退方案：直接设置innerHTML
          const editor = this.$refs.editor.querySelector('.ql-editor');
          if (editor) {
            editor.innerHTML = htmlContent;
          }
        }
      }
    },

    // 预处理HTML内容，确保样式能被Quill正确解析
    preprocessHtmlContent(htmlContent) {
      // 创建临时DOM元素来处理HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = htmlContent;

      // 处理所有元素，确保样式属性正确
      this.processElementStyles(tempDiv);

      return tempDiv.innerHTML;
    },

    // 递归处理元素样式
    processElementStyles(element) {
      if (element.nodeType === Node.ELEMENT_NODE) {
        // 处理style属性
        if (element.style) {
          // 确保字体大小格式正确
          if (element.style.fontSize && !element.style.fontSize.endsWith('px')) {
            const fontSize = parseFloat(element.style.fontSize);
            if (!isNaN(fontSize)) {
              element.style.fontSize = fontSize + 'px';
            }
          }

          // 处理字体族
          if (element.style.fontFamily) {
            // 移除引号并标准化字体名称
            let fontFamily = element.style.fontFamily.replace(/['"]/g, '');
            // 映射常见字体名称
            const fontMap = {
              '宋体': 'SimSun',
              '黑体': 'SimHei',
              '微软雅黑': 'Microsoft-YaHei',
              '楷体': 'KaiTi_GB2312',
              '仿宋': 'FangSong_GB2312',
              '方正小标宋': 'FangZheng_XiaoBiaoSong'
            };
            if (fontMap[fontFamily]) {
              element.style.fontFamily = fontMap[fontFamily];
            }
          }
        }

        // 递归处理子元素
        for (let child of element.children) {
          this.processElementStyles(child);
        }
      }
    },

    // 添加匹配粘贴板事件禁止图片粘贴
    handleCustomMatcher(node, Delta) {
      const ops = [];
      Delta.ops.forEach((op) => {
        // 如果粘贴了图片，这里会是一个对象，所以可以这样处理
        if (op.insert && typeof op.insert === "string") {
          ops.push({
            insert: op.insert, // 文字内容
            attributes: op.attributes, // 文字样式（包括背景色和文字颜色等）
          });
        } else {
          // 判断图片是否是base64
          if (op.insert && op.insert.image && op.insert.image.indexOf("base64") > -1) {
            // 发出事件通知父组件
            this.$emit("on-paste-image-blocked", "禁止粘贴图片");
            console.warn("禁止粘贴base64图片");
          } else {
            ops.push({
              insert: op.insert, // 图片内容
              attributes: op.attributes, // 图片样式（包括宽高等）
            });
          }
        }
      });
      Delta.ops = ops;
      return Delta;
    },

    // 处理SPAN标签，保留样式属性
    handleSpanMatcher(node, delta) {
      const attributes = {};

      // 提取style属性
      if (node.style) {
        if (node.style.color) {
          attributes.color = node.style.color;
        }
        if (node.style.backgroundColor) {
          attributes.background = node.style.backgroundColor;
        }
        if (node.style.fontSize) {
          attributes.size = node.style.fontSize;
        }
        if (node.style.fontFamily) {
          attributes.font = node.style.fontFamily;
        }
        if (node.style.fontWeight === 'bold' || node.style.fontWeight >= 600) {
          attributes.bold = true;
        }
        if (node.style.fontStyle === 'italic') {
          attributes.italic = true;
        }
        if (node.style.textDecoration === 'underline') {
          attributes.underline = true;
        }
      }

      // 提取class属性中的样式
      if (node.className) {
        const classes = node.className.split(' ');
        classes.forEach(cls => {
          if (cls.startsWith('ql-size-')) {
            attributes.size = cls.replace('ql-size-', '');
          }
          if (cls.startsWith('ql-font-')) {
            attributes.font = cls.replace('ql-font-', '');
          }
        });
      }

      // 应用属性到delta
      if (Object.keys(attributes).length > 0) {
        delta.ops.forEach(op => {
          if (op.insert && typeof op.insert === 'string') {
            op.attributes = { ...op.attributes, ...attributes };
          }
        });
      }

      return delta;
    },

    // 处理段落标签
    handleParagraphMatcher(node, delta) {
      const attributes = {};

      if (node.style && node.style.textAlign) {
        attributes.align = node.style.textAlign;
      }

      if (Object.keys(attributes).length > 0) {
        delta.ops.forEach(op => {
          if (op.insert && typeof op.insert === 'string') {
            op.attributes = { ...op.attributes, ...attributes };
          }
        });
      }

      return delta;
    },

    // 处理DIV标签
    handleDivMatcher(node, delta) {
      return this.handleParagraphMatcher(node, delta);
    },

    // 处理STRONG标签
    handleStrongMatcher(node, delta) {
      delta.ops.forEach(op => {
        if (op.insert && typeof op.insert === 'string') {
          op.attributes = { ...op.attributes, bold: true };
        }
      });
      return delta;
    },

    // 处理B标签
    handleBoldMatcher(node, delta) {
      return this.handleStrongMatcher(node, delta);
    },

    // 处理EM标签
    handleEmMatcher(node, delta) {
      delta.ops.forEach(op => {
        if (op.insert && typeof op.insert === 'string') {
          op.attributes = { ...op.attributes, italic: true };
        }
      });
      return delta;
    },

    // 处理I标签
    handleItalicMatcher(node, delta) {
      return this.handleEmMatcher(node, delta);
    },

    // 处理U标签
    handleUnderlineMatcher(node, delta) {
      delta.ops.forEach(op => {
        if (op.insert && typeof op.insert === 'string') {
          op.attributes = { ...op.attributes, underline: true };
        }
      });
      return delta;
    },
    async exportToWord(filename = this.exportFilename) {
      if (!this.Quill) {
        this.$message.error("编辑器未初始化");
        return;
      }

      try {
        // 显示加载状态
        this.$message({
          message: "正在生成Word文档...",
          type: "info",
          duration: 2000
        });

        // 获取编辑器HTML内容
        const htmlContent = this.Quill.root.innerHTML;

        // 使用html-to-docx转换为Word文档
        const docxBlob = await HTMLtoDOCX(htmlContent, {
          orientation: 'portrait',
          margins: {
            top: 720,    // 1英寸 = 1440 twips，这里设置0.5英寸
            right: 720,
            bottom: 720,
            left: 720
          }
        });

        // 确保文件名有正确的扩展名
        const finalFilename = filename.endsWith('.docx') ? filename : `${filename}.docx`;

        // 下载文件
        saveAs(docxBlob, finalFilename);

        this.$message.success("Word文档导出成功！");

        // 触发导出成功事件
        this.$emit("on-export", {
          filename: finalFilename,
          content: htmlContent,
          text: this.Quill.getText(),
          success: true
        });

      } catch (error) {
        console.error("导出Word失败:", error);
        // this.$message.error("导出Word失败，请重试");

        // 触发导出失败事件
        // this.$emit("on-export", {
        //   filename: filename || "document.docx",
        //   content: this.Quill.root.innerHTML,
        //   text: this.Quill.getText(),
        //   success: false,
        //   error: error
        // });
      }

      // 注释掉原先的导出逻辑
      // 原先是触发导出事件，让父组件处理实际的导出逻辑
      // this.$emit("on-export", {
      //   filename: filename || "document.docx",
      //   content: this.Quill.root.innerHTML,
      //   text: this.Quill.getText()
      // });
    },
    addExportButton() {
      // 获取工具栏容器
      const toolbar = this.Quill.getModule("toolbar").container;

      // 创建导出按钮
      const exportButton = document.createElement("button");
      exportButton.className = "ql-exportWord";
      exportButton.innerHTML = '<svg viewBox="0 0 1024 1024" width="16" height="16"><path d="M505.7 661a8 8 0 0 0 12.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9L505.7 661z"></path><path d="M878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"></path></svg>';
      exportButton.type = "button";
      exportButton.title = "导出Word";

      // 添加点击事件
      exportButton.addEventListener("click", this.onExportClick.bind(this));

      // 将按钮添加到工具栏最后面
      toolbar.appendChild(exportButton);

      console.log("导出按钮已添加到工具栏");
    },
    onExportClick() {
      console.log("onExportClick 被触发了");
      console.log("this.Quill:", this.Quill);
      console.log("showExport:", this.showExport);
      this.exportToWord();
    },
  },
};
</script>

<style>

.ql-container.ql-snow{
  border: none !important;
}

.editor {
  white-space: pre-wrap !important;
  line-height: normal !important;
  /* 设置A4纸张宽度 (210mm ≈ 794px at 96DPI) */
  width: 794px !important;
  margin: 0 auto;
}

.editor .ql-toolbar {
  white-space: pre-wrap !important;
  line-height: normal !important;
  /* 工具栏也设置为A4宽度 */
  width: 794px !important;
  margin: 0 auto;
}

.editor .ql-container {
  /* 编辑区域也设置为A4宽度 */
  width: 794px !important;
  margin: 0 auto;
}
.quill-img {
  display: none;
}
.ql-snow .ql-tooltip[data-mode="link"]::before {
  content: "请输入链接地址:";
}
.ql-snow .ql-tooltip.ql-editing a.ql-action::after {
  border-right: 0px;
  content: "保存";
  padding-right: 0px;
}

.ql-snow .ql-tooltip[data-mode="video"]::before {
  content: "请输入视频地址:";
}

/* Word字号样式 - 默认显示小四 */
.ql-snow .ql-picker.ql-size .ql-picker-label::before,
.ql-snow .ql-picker.ql-size .ql-picker-item::before {
  content: "小四";
}

/* 初号 */
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="56px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="56px"]::before {
  content: "初号";
}

/* 小初 */
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="48px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="48px"]::before {
  content: "小初";
}

/* 一号 */
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="34.7px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="34.7px"]::before {
  content: "一号";
}

/* 小一 */
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="32px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="32px"]::before {
  content: "小一";
}

/* 二号 */
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="29.3px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="29.3px"]::before {
  content: "二号";
}

/* 小二 */
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="24px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="24px"]::before {
  content: "小二";
}

/* 三号 */
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="21.3px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="21.3px"]::before {
  content: "三号";
}

/* 小三 */
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="20px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="20px"]::before {
  content: "小三";
}

/* 四号 */
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="18.7px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="18.7px"]::before {
  content: "四号";
}

/* 小四 */
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="16px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="16px"]::before {
  content: "小四";
}

/* 五号 */
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="14px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="14px"]::before {
  content: "五号";
}

/* 小五 */
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="12px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="12px"]::before {
  content: "小五";
}

/* 六号 */
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="10px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="10px"]::before {
  content: "六号";
}

/* 小六 */
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="8.7px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="8.7px"]::before {
  content: "小六";
}

/* 七号 */
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="7.3px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="7.3px"]::before {
  content: "七号";
}

/* 八号 */
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="6.7px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="6.7px"]::before {
  content: "八号";
}

.ql-snow .ql-picker.ql-header .ql-picker-label::before,
.ql-snow .ql-picker.ql-header .ql-picker-item::before {
  content: "文本";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
  content: "标题1";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
  content: "标题2";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
  content: "标题3";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
  content: "标题4";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
  content: "标题5";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
  content: "标题6";
}

/* 字体选择器默认显示 */
.ql-snow .ql-picker.ql-font .ql-picker-label::before,
.ql-snow .ql-picker.ql-font .ql-picker-item::before {
  content: "宋体";
  font-family: "SimSun";
}

/* 宋体 */
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="SimSun"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="SimSun"]::before {
  content: "宋体";
  font-family: "SimSun";
}

/* 黑体 */
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="SimHei"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="SimHei"]::before {
  content: "黑体";
  font-family: "SimHei";
}

/* 微软雅黑 */
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="Microsoft-YaHei"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="Microsoft-YaHei"]::before {
  content: "微软雅黑";
  font-family: "Microsoft YaHei";
}

/* 楷体 */
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="KaiTi_GB2312"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="KaiTi_GB2312"]::before {
  content: "楷体";
  font-family: "KaiTi_GB2312";
}

/* 仿宋 */
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="FangSong_GB2312"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="FangSong_GB2312"]::before {
  content: "仿宋";
  font-family: "FangSong_GB2312";
}

/* 方正小标宋 */
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="FangZheng_XiaoBiaoSong"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="FangZheng_XiaoBiaoSong"]::before {
  content: "方正小标宋";
  font-family: "FangZheng_XiaoBiaoSong";
}

/* 导出Word自定义按钮样式 */
.ql-snow .ql-toolbar button.ql-exportWord {
  width: 28px;
  height: 28px;
  padding: 5px;
  border: 1px solid #ccc;
  border-radius: 3px;
  background: #fff;
  cursor: pointer;
  margin-left: 5px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.ql-snow .ql-toolbar button.ql-exportWord:hover {
  background: #f5f5f5;
  border-color: #409eff;
}

.ql-snow .ql-toolbar button.ql-exportWord svg {
  fill: #666;
  transition: fill 0.2s;
}

.ql-snow .ql-toolbar button.ql-exportWord:hover svg {
  fill: #409eff;
}

.ql-snow .ql-toolbar button.ql-exportWord::after {
  content: "";
}
</style>
