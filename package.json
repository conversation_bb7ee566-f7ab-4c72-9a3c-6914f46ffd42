{"name": "ruoyi", "version": "3.4.0", "description": "安陆智能体", "author": "若依", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@riophae/vue-treeselect": "0.4.0", "axios": "0.21.0", "clipboard": "2.0.6", "core-js": "3.8.1", "cron-parser": "^3.3.0", "echarts": "^5.5.1", "echarts-gl": "^2.0.9", "echarts-wordcloud": "^2.1.0", "element-ui": "2.15.5", "file-saver": "2.0.4", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "html-to-docx": "^1.8.0", "iview": "^3.5.4", "jquery": "^3.7.1", "js-beautify": "1.13.0", "js-cookie": "2.2.1", "jsencrypt": "3.0.0-rc.1", "jsmind": "^0.4.8", "less-loader": "^7.3.0", "mavon-editor": "^2.9.1", "moment": "^2.30.1", "nprogress": "0.2.0", "quill": "1.3.7", "quill-to-word": "^1.3.0", "quill-vue2-editor": "2.0.0-dev", "screenfull": "5.0.2", "sortablejs": "1.10.2", "sql-formatter": "4.0.2", "tinymce": "^7.9.1", "vue": "2.6.12", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-router": "3.4.9", "vue-style-loader": "^4.1.3", "vuedraggable": "2.24.3", "vuex": "3.6.0"}, "devDependencies": {"compression-webpack-plugin": "^6.1.2", "terser-webpack-plugin": "^4.2.3", "@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-import": "^1.13.5", "chalk": "4.1.0", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "less": "^4.1.2", "less-loader": "^7.3.0", "lint-staged": "10.5.3", "node-less": "^1.0.0", "runjs": "4.4.2", "sass": "1.32.0", "sass-loader": "10.1.0", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie <= 11"]}