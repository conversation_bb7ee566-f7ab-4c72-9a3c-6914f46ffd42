<template>
  <div class="app-container">
    <el-dialog
      :title="title"
      :modal-append-to-body="false"
      :close-on-click-modal="true"
      :visible.sync="visible"
      width="40%">
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="110px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="审核人" prop="username">
              <el-input v-model="dataForm.username" disabled></el-input>
            </el-form-item></el-col>
          <el-col :span="24">
            <el-form-item label="审核意见" prop="shyj">
              <el-input type="textarea" v-model="dataForm.shyj" placeholder="输入审核意见" :autosize="{ minRows: 3, maxRows: 10}"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row style="display: flex;justify-content: end;">
          <el-button @click="visible = false">取消</el-button>
          <el-button type="primary" @click="save()">保存</el-button>
        </el-row>
      </el-form>

    </el-dialog>
  </div>
</template>

<script>
  import { refuse } from "@/api/screen/zbgl";

  export default {
    data() {
      return {
        visible: false,
        title: '',
        id: null,
        dataForm: {
          username: null,
          shyj:'',
        },
        dataRule: {
          shyj: [
            {required: true, message: '审核意见不能为空', trigger: 'blur'}
          ],
        }
      }
    },
    mounted() {},
    created() {},
    methods: {
      // 获取数据列表
      init(id) {
        this.visible = true
        this.title = '审核不通过意见'
        this.$refs['dataForm'].resetFields()
        this.id = id
        this.dataForm.username = window.sessionStorage.getItem('userName')
      },

      save(){
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            refuse({
              id:this.id,
              auditmark:this.dataForm.shyj
            }).then(response => {
              if (response.successful) {
                this.visible = false
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  // duration: 1500,
                  onClose: () => {
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                console.log('ERROR', response)
                this.$message.error('操作失败')
              }
            });
          }
        })
      }
    }
  }
</script>
