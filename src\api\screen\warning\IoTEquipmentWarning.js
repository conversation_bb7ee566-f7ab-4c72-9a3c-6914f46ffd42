import request from '@/utils/request'

// 查询设备预警列表
export function listYwtgYj(query) {
  return request({
    url: '/kfqywtg/ywtgYj/listWl',
    method: 'get',
    params: query
  })
}

// 查询产品列表
export function getPkList() {
  return request({
    url: '/kfqywtg/pk/list',
    method: 'get'
  })
}

// 查询设备详细信息
export function getDevice(id) {
  return request({
    url: `/kfqywtg/pk/${id}`,
    method: 'get'
  })
}
