import request from '@/utils/request'

// 查询设备预警列表
export function listYwtgYj(query) {
  return request({
    url: '/kfqywtg/ywtgYj/list',
    method: 'get',
    params: query
  })
}

// 查询设备预警详细
export function getYwtgYj(id) {
  return request({
    url: '/kfqywtg/ywtgYj/' + id,
    method: 'get'
  })
}

// 新增设备预警
export function addYwtgYj(data) {
  return request({
    url: '/kfqywtg/ywtgYj/add',
    method: 'post',
    data: data
  })
}

// 修改设备预警
export function updateYwtgYj(data) {
  return request({
    url: '/kfqywtg/ywtgYj/edit',
    method: 'post',
    data: data
  })
}

// 删除设备预警
export function delYwtgYj(id) {
  return request({
    url: '/kfqywtg/ywtgYj/remove/' + id,
    method: 'post'
  })
}

// 导出设备预警
export function exportYwtgYj(query) {
  return request({
    url: '/kfqywtg/ywtgYj/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function templatePetition() {
  return request({
    url: '/kfqywtg/ywtgYj/importTemplate',
    method: 'post',
    responseType: 'blob'
  })
}
