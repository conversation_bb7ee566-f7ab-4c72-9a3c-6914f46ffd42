<template>
  <div class="app-container">
    <el-dialog
      :title="dataForm.title"
      :modal-append-to-body="false"
      :close-on-click-modal="true"
      :visible.sync="visible"
      width="75%">
      <el-table
        :data="dataList"
        v-loading="dataListLoading"
        style="width: 100%;">
        <el-table-column
          header-align="center"
          align="center"
          width="150"
          type="index"
          label="序号">
        </el-table-column>
        <el-table-column
          prop="applyBy"
          header-align="center"
          align="center"
          label="填报人员">
        </el-table-column>
        <el-table-column
          prop="applyTime"
          header-align="center"
          align="center"
          label="填报时间">
        </el-table-column>
        <el-table-column
          prop="auditmark"
          header-align="center"
          align="center"
          width="200"
          label="审核意见">
        </el-table-column>
        <el-table-column
          prop="auditor"
          header-align="center"
          align="center"
          label="审核人">
        </el-table-column>
        <el-table-column
          prop="auditime"
          header-align="center"
          align="center"
          label="审核时间">
        </el-table-column>
        <el-table-column
          prop="isAudit"
          header-align="center"
          align="center"
          label="审核状态">
          <template slot-scope="scope">
            <span v-if="scope.row.isAudit == 0">待审核</span>
            <span v-else-if="scope.row.isAudit == 1">审核通过</span>
            <span v-else-if="scope.row.isAudit == 9">审核不通过</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="pageNum"
        :limit.sync="pageSize"
        @pagination="getDataList"
      />
    </el-dialog>
  </div>
</template>

<script>
  import { auditRecords, auditList, adopt, refuse } from "@/api/screen/zbgl";

  export default {
    data() {
      return {
        visible: false,
        dataForm: {
          title: '',
          bm: null
        },
        // 显示搜索条件
        total: 0,
        pageNum: 1,
        pageSize: 10,
        dataListLoading: false,
        dataList: [],
      }
    },
    mounted() {},
    created() {},
    methods: {
      // 获取数据列表
      init(bm) {
        this.visible = true
        this.dataForm.title = '审核记录'
        this.dataForm.bm = bm
        this.getDataList()
      },
      // 获取数据列表
      getDataList() {
        this.dataListLoading = true
        auditRecords(this.dataForm.bm).then(response => {
          this.dataList = response.rows;
          // this.total = response.total;
          this.dataListLoading = false
        });
      },
      
    }
  }
</script>
