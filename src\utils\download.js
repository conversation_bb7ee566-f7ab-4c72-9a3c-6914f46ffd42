/**
 * 文件下载工具函数
 */

/**
 * 下载blob文件
 * @param {Blob} blob - 文件blob对象
 * @param {string} filename - 文件名
 * @param {string} mimeType - MIME类型
 */
export function downloadBlob(blob, filename, mimeType) {
  try {
    // 检查blob是否有效
    if (!blob || !(blob instanceof Blob)) {
      throw new Error("无效的文件数据");
    }

    // 创建新的blob确保MIME类型正确
    const file = new Blob([blob], { type: mimeType });

    // 创建下载链接
    const url = window.URL.createObjectURL(file);
    const link = document.createElement("a");

    link.href = url;
    link.download = filename;
    link.style.display = "none";

    // 添加到DOM并触发下载
    document.body.appendChild(link);
    link.click();

    // 延迟清理，确保下载开始
    setTimeout(() => {
      try {
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } catch (e) {
        console.warn("清理下载链接时出错:", e);
      }
    }, 100);

    return true;
  } catch (error) {
    console.error("下载文件失败:", error);
    throw error;
  }
}

/**
 * 下载Word文档
 * @param {Blob} blob - Word文件blob
 * @param {string} filename - 文件名（不含扩展名）
 */
export function downloadWordDocument(blob, filename) {
  const mimeType =
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
  const fullFilename = filename.endsWith(".docx")
    ? filename
    : `${filename}.docx`;
  return downloadBlob(blob, fullFilename, mimeType);
}

/**
 * 从响应头中提取文件名
 * @param {Object} response - axios响应对象
 * @returns {string} 文件名
 */
export function extractFilenameFromResponse(response) {
  try {
    const contentDisposition = response.headers["content-disposition"];
    if (contentDisposition) {
      // 匹配 filename="xxx" 或 filename*=UTF-8''xxx
      const filenameMatch = contentDisposition.match(
        /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
      );
      if (filenameMatch && filenameMatch[1]) {
        let filename = filenameMatch[1].replace(/['"]/g, "");
        // 处理UTF-8编码的文件名
        if (filename.startsWith("UTF-8''")) {
          filename = decodeURIComponent(filename.substring(7));
        }
        return filename;
      }
    }
  } catch (error) {
    console.warn("提取文件名失败:", error);
  }
  return null;
}

/**
 * 处理文件下载错误
 * @param {Error} error - 错误对象
 * @returns {string} 用户友好的错误消息
 */
export function handleDownloadError(error) {
  if (!error) return "下载失败，未知错误";

  const message = error.message || error.toString();

  if (message.includes("ERR_RESPONSE_HEADERS_MULTIPLE_CONTENT_DISPOSITION")) {
    return "服务器响应头配置错误，请联系管理员检查后端Content-Disposition设置";
  }

  if (message.includes("ERR_RESPONSE_HEADERS_MULTIPLE_CONTENT_TYPE")) {
    return "服务器响应头配置错误，请联系管理员检查后端Content-Type设置";
  }

  if (message.includes("Network Error")) {
    return "网络连接错误，请检查网络后重试";
  }

  if (message.includes("timeout")) {
    return "下载超时，文件可能较大，请重试";
  }

  if (message.includes("404")) {
    return "文件不存在或已被删除";
  }

  if (message.includes("403")) {
    return "没有权限下载此文件";
  }

  if (message.includes("500")) {
    return "服务器内部错误，请稍后重试";
  }

  if (message.includes("ERR_INVALID_RESPONSE")) {
    return "服务器响应格式错误，请联系管理员";
  }

  return `下载失败：${message}`;
}

/**
 * 使用原生fetch下载文件（备用方案）
 * @param {string} url - 下载URL
 * @param {string} filename - 文件名
 * @param {Object} headers - 请求头
 */
export async function downloadWithFetch(url, filename, headers = {}) {
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: {
        Authorization: headers.Authorization || "",
        Accept:
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "Cache-Control": "no-cache",
        Pragma: "no-cache",
        ...headers,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const blob = await response.blob();
    downloadWordDocument(blob, filename);
    return true;
  } catch (error) {
    console.error("Fetch下载失败:", error);
    throw error;
  }
}
