<template>
  <el-dialog title="指标项选择" :modal-append-to-body="false" :close-on-click-modal="false" :visible.sync="visible"
    width="50%">
    <el-form :model="dataForm" ref="dataForm" label-width="90px">
      <el-row>
        <el-col :span="8">
          <el-form-item label="类别" prop="level0">
            <el-select v-model="dataForm.level0" filterable placeholder=" -- 不选择 -- " clearable @change="changeSelect(0)"
              @clear="getSelect">
              <el-option v-for="(item, index) in typeOptions" :key="index" :label="item" :value="item">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="一级指标" prop="level1">
            <el-select v-model="dataForm.level1" filterable placeholder=" -- 不选择 -- " clearable @change="changeSelect(1)"
              @clear="changeSelect(0)">
              <el-option v-for="(item, index) in label1Options" :key="index" :label="item" :value="item">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="二级指标" prop="level2">
            <el-select v-model="dataForm.level2" filterable placeholder=" -- 不选择 -- " clearable>
              <el-option v-for="(item, index) in label2Options" :key="index" :label="item" :value="item">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="三级指标" prop="level3">
            <el-input v-model="dataForm.level3" size="small" placeholder="请输入三级指标" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" :offset="1">
          <el-button type="primary" size="small" icon="el-icon-search" @click="search">搜索</el-button>
          <el-button type="primary" size="small" icon="el-icon-refresh" @click="reset">重置</el-button>
        </el-col>
      </el-row>
    </el-form>
    <el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" style="width: 100%" height="350"
      @select="handleSelect"
      v-loading="loading"
      element-loading-text="拼命加载中"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(225, 225, 225, 0.8)">
      <el-table-column type="selection" width="55">
      </el-table-column>
      <el-table-column prop="level3" align="center" label="三级指标" />
      <el-table-column prop="level2" align="center" label="二级指标" />
      <el-table-column prop="level1" align="center" label="一级指标" />
      <el-table-column prop="level0" align="center" label="类别" />
    </el-table>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" size="small" @click="determine">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getCsyxsmtz, getCsyxsmtzLevel } from "@/api/screen/zbgl";
import { param } from "@/utils";
export default {
  props: {
    zbx: {
      type: Array,
      default: ()=>{
        return []
      }
    },
  },
  data() {
    return {
      loading: false,
      visible: true,
      dataForm: {
        level0: null,
        level1: null,
        level2: null,
        level3: null
      },
      typeOptions: [],
      label1Options: [],
      label2Options: [],
      tableData: [],
      multipleSelection: [],
    }
  },
  created() {},
  methods: {
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.multipleSelection = this.zbx
        this.getSelect()
        this.getList()
      })
    },
    getList() {
      this.loading = true
      this.tableData = []
      getCsyxsmtz(this.dataForm).then(res => {
        this.loading = false
        this.tableData = res.data
        this.toggleSelection(this.multipleSelection)
      })
    },
    getSelect() {
      getCsyxsmtzLevel().then(res => {
        this.typeOptions = res.data.level0
        this.label1Options = res.data.level1
        this.label2Options = res.data.level2
      })
    },
    changeSelect(index) {
      let params = {}
      if (index == 0) {
        if (this.dataForm.level0) {
          getCsyxsmtzLevel({ 'level0': this.dataForm.level0 }).then(res => {
            this.label1Options = res.data.level1
            this.label2Options = res.data.level2
          })
        }
      } else {
        if (this.dataForm.level1) {
          getCsyxsmtzLevel({ 'level1': this.dataForm.level1 }).then(res => {
            this.label2Options = res.data.level2
          })
        }
      }
    },
    search() {
      console.log(this.dataForm);
      this.getList()
    },
    reset() {
      this.resetForm("dataForm");
      this.search();
    },
    determine() {
      this.visible = false
      console.log(this.multipleSelection);
      this.some(this.multipleSelection)
    },
    //去重
    some(arr) {
      let some = [];
      arr.forEach(el => {
        if (!some.some(e => e.indicatorId == el.indicatorId)) {
          some.push(el);
        }
      });
      this.$emit('zbxList', some)
    },
    handleSelect(rows, row){
      let selected = rows.length && rows.indexOf(row) !== -1
	    console.log(selected)  // true就是选中，0或者false是取消选中
      if(selected){
        this.multipleSelection.push(row)
      }else{
        this.multipleSelection = this.multipleSelection.filter(x => x.indicatorId!==row.indicatorId);
      }
    },
    toggleSelection(rows) {
      if (rows) {
        rows.forEach(item => {
          this.$nextTick(() => {
            this.tableData.find(obj => {
              if (item.indicatorId === obj.indicatorId) {
                this.$refs.multipleTable.toggleRowSelection(obj, true)
              }
            })
          })
        })
      } else {
        this.$refs.multipleTable.clearSelection();
      }
    },
  }
}
</script>
<style scoped lang="scss">
.el-button {
  margin: 3px 3px 3px 12px;
}
::v-deep .el-table__header-wrapper .el-checkbox {
  // display: none;//设置不成功，页面卡顿
  visibility: hidden;
}
</style>
