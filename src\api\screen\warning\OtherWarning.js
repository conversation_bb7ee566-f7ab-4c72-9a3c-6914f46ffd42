import request from '@/utils/request'

// 查询设备预警列表
export function listYwtgYj(query) {
  return request({
    url: '/kfqywtg/ywtgRyYj/list',
    method: 'get',
    params: query
  })
}

// 查询设备预警详细
export function getYwtgYj(id) {
  return request({
    url: '/kfqywtg/ywtgRyYj/' + id,
    method: 'get'
  })
}

// 修改设备预警
export function updateYwtgYj(data) {
  return request({
    url: '/kfqywtg/ywtgRyYj/edit',
    method: 'post',
    data: data
  })
}

// 导出设备预警
export function exportYwtgYj(query) {
  return request({
    url: '/kfqywtg/ywtgRyYj/export',
    method: 'get',
    params: query
  })
}
