<template>
  <div class="mydrafts-page mobile-page">
    <!-- 移动端顶部栏 -->
    <div class="mobile-header">
      <div class="header-title">我的稿件</div>
      <div class="header-actions">
        <el-button
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="handleNew"
          class="new-btn"
        >
          新建
        </el-button>
      </div>
    </div>

    <!-- 移动端搜索栏 -->
    <div class="mobile-search">
      <el-input
        v-model="queryParams.keyword"
        clearable
        placeholder="搜索稿件..."
        prefix-icon="el-icon-search"
        @keyup.enter.native="handleQuery"
        @clear="handleQuery"
        size="small"
      />
    </div>

    <!-- 移动端横幅引导 -->
    <div
      class="mobile-banner"
      @click="handleNew"
      v-if="!list || list.length === 0"
    >
      <div class="banner-content">
        <i class="el-icon-plus banner-icon"></i>
        <div class="banner-title">开始创建您的第一份稿件</div>
        <div class="banner-desc">AI智能写作助手，让公文写作更高效</div>
      </div>
    </div>

    <!-- 移动端内容区域 -->
    <mobile-pull-refresh
      :refreshing="isRefreshing"
      @refresh="handleRefresh"
      class="mobile-content"
    >
      <div
        ref="scrollContainer"
        @scroll="handleScroll"
        class="scroll-content"
        v-loading="loading"
      >
        <div class="mobile-list" v-if="list && list.length > 0">
          <div
            class="mobile-card"
            v-for="item in list"
            :key="item.id"
            @click="editDraft(item)"
          >
            <div class="card-header">
              <div class="card-title">{{ item.title || "未命名稿件" }}</div>
              <div class="card-time">
                {{
                  formatDate(
                    item.createTime ||
                      item.updateTime ||
                      item.cTime ||
                      item.uTime
                  )
                }}
              </div>
            </div>
            <div class="card-content" v-if="item.content">
              <div
                class="content-preview"
                v-html="getCleanContentPreview(item.content)"
              ></div>
            </div>
            <div class="card-footer">
              <div class="footer-left">
                <el-tag size="mini" v-if="item.gwType" type="primary">{{
                  item.gwType
                }}</el-tag>
              </div>
              <div class="footer-right">
                <el-button
                  type="text"
                  size="mini"
                  icon="el-icon-edit"
                  @click.stop="editDraft(item)"
                >
                  编辑
                </el-button>
                <el-button
                  type="text"
                  size="mini"
                  icon="el-icon-delete"
                  class="danger"
                  @click.stop="removeDraft(item)"
                >
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else-if="!loading" class="mobile-empty">
          <i class="el-icon-document-copy empty-icon"></i>
          <div class="empty-text">暂无稿件数据</div>
          <el-button type="primary" @click="handleNew" size="small"
            >立即创建</el-button
          >
        </div>

        <!-- 加载更多提示 -->
        <div class="load-more" v-if="list && list.length > 0">
          <div v-if="isLoadingMore" class="loading-text">
            <i class="el-icon-loading"></i>
            正在加载...
          </div>
          <div v-else-if="hasMoreData" class="load-text" @click="loadMore">
            点击加载更多
          </div>
          <div v-else class="no-more-text">没有更多数据了</div>
        </div>
      </div>
    </mobile-pull-refresh>
  </div>
</template>

<script>
import {
  listZntXz,
  delZntXz,
  exportZntXz,
  downloadZntXzWord,
} from "@/api/gongwen/mydrafts";
import MobilePullRefresh from "@/components/MobilePullRefresh";

export default {
  name: "MyDrafts",
  components: {
    MobilePullRefresh,
  },
  data() {
    return {
      loading: false,
      list: [],
      total: 0,
      activeTab: "zntxz", // 当前激活的标签页
      queryParams: {
        pageNum: 1,
        pageSize: 5,
        keyword: "",
      },
      // 移动端下拉加载相关
      hasMore: true,
      isLoadingMore: false,
      isRefreshing: false,
    };
  },
  computed: {
    // 检查是否还有更多数据
    hasMoreData() {
      return this.list.length < this.total;
    },
  },
  created() {
    this.getList();
  },
  mounted() {
    // 监听滚动事件
    this.$nextTick(() => {
      if (this.$refs.scrollContainer) {
        this.$refs.scrollContainer.addEventListener(
          "scroll",
          this.handleScroll
        );
      }
    });
  },
  beforeDestroy() {
    // 移除滚动监听
    if (this.$refs.scrollContainer) {
      this.$refs.scrollContainer.removeEventListener(
        "scroll",
        this.handleScroll
      );
    }
  },
  methods: {
    handleNew() {
      // 跳转到智能体写作创建页面
      this.$router.push({ path: "/app/editor" });
    },
    viewDraft(item) {
      this.$router.push({
        path: `/app/editor/${item.id}`,
      });
    },
    editDraft(item) {
      this.$router.push({
        path: `/app/editor/${item.id}`,
      });
    },
    async removeDraft(item) {
      try {
        await this.$confirm(
          `确认删除稿件《${item.title || "未命名稿件"}》吗？`,
          "提示",
          { type: "warning" }
        );
        await delZntXz(item.id);
        this.msgSuccess("删除成功");
        this.getList();
      } catch (e) {
        // 取消或失败均忽略
      }
    },

    // 导出智能体写作列表
    async handleExport() {
      try {
        this.loading = true;
        await exportZntXz(this.queryParams);
        this.msgSuccess("导出成功");
      } catch (error) {
        this.msgError("导出失败");
      } finally {
        this.loading = false;
      }
    },
    // 下载Word文档
    async downloadWord(item) {
      try {
        this.loading = true;
        const response = await downloadZntXzWord(item.id);
        // 创建下载链接
        const blob = new Blob([response]);
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `${item.title || "智能体写作"}.docx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        this.msgSuccess("下载成功");
      } catch (error) {
        this.msgError("下载失败");
      } finally {
        this.loading = false;
      }
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.list = []; // 清空列表
      this.hasMore = true;
      this.getList();
    },
    handlePagination(pagination) {
      this.queryParams.pageNum = pagination.page;
      this.queryParams.pageSize = pagination.limit;
      this.getList();
    },
    // 下拉刷新处理
    async handleRefresh() {
      this.isRefreshing = true;
      this.queryParams.pageNum = 1;
      this.list = [];
      this.hasMore = true;

      try {
        await this.getList();
      } finally {
        this.isRefreshing = false;
      }
    },
    // 移动端滚动处理
    handleScroll(event) {
      const { scrollTop, scrollHeight, clientHeight } = event.target;
      // 当滚动到底部附近时触发加载更多
      if (
        scrollTop + clientHeight >= scrollHeight - 100 &&
        this.hasMoreData &&
        !this.isLoadingMore
      ) {
        this.loadMore();
      }
    },
    // 加载更多数据
    async loadMore() {
      if (this.isLoadingMore || !this.hasMoreData) return;

      this.isLoadingMore = true;
      this.queryParams.pageNum += 1;

      try {
        const zntParams = {
          pageNum: this.queryParams.pageNum,
          pageSize: this.queryParams.pageSize,
        };
        if (this.queryParams.keyword) {
          zntParams.title = this.queryParams.keyword;
        }

        const res = await listZntXz(zntParams);
        const newList = res.rows || res.data || [];

        // 追加新数据到现有列表
        this.list = [...this.list, ...newList];
        this.total = res.total || this.list.length;

        // 检查是否还有更多数据
        this.hasMore = this.list.length < this.total;
      } catch (error) {
        console.error("加载更多失败:", error);
        this.queryParams.pageNum -= 1; // 回退页码
      } finally {
        this.isLoadingMore = false;
      }
    },
    async getList(isLoadMore = false) {
      if (!isLoadMore) {
        this.loading = true;
      }

      try {
        // 构建智能体写作查询参数
        const zntParams = {
          pageNum: this.queryParams.pageNum,
          pageSize: this.queryParams.pageSize,
        };
        // 如果有关键字搜索，添加到title字段
        if (this.queryParams.keyword) {
          zntParams.title = this.queryParams.keyword;
        }
        const res = await listZntXz(zntParams);
        const newList = res.rows || res.data || [];

        if (isLoadMore) {
          // 加载更多时追加数据
          this.list = [...this.list, ...newList];
        } else {
          // 首次加载或刷新时替换数据
          this.list = newList;
        }

        this.total = res.total || this.list.length;
        this.hasMore = this.list.length < this.total;
      } finally {
        if (!isLoadMore) {
          this.loading = false;
        }
      }
    },
    formatDate(val) {
      if (!val) return "";
      // 支持时间戳或字符串
      const d =
        typeof val === "number"
          ? new Date(val)
          : new Date(val.replace(/-/g, "/"));
      const y = d.getFullYear();
      const m = `${d.getMonth() + 1}`.padStart(2, "0");
      const da = `${d.getDate()}`.padStart(2, "0");
      return `${y}-${m}-${da}`;
    },
    // 获取清理后的内容预览
    getCleanContentPreview(content) {
      if (!content) return "";

      // 移除 style 和 script 标签及其内容
      let cleanContent = content
        .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, "")
        .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, "")
        .replace(/\sclass\s*=\s*["'][^"']*["']/gi, "")
        // 将所有开始标签转换为 <p>
        .replace(/<[^\/][^>]*>/gi, "<p>")
        // 将所有结束标签转换为 </p>
        .replace(/<\/[^>]+>/gi, "</p>");

      // 截取前100个字符
      const preview = cleanContent.substring(0, 100);
      return preview + (cleanContent.length > 100 ? "..." : "");
    },
  },
};
</script>

<style lang="scss" scoped>
.mydrafts-page {
  padding: 0;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;

  // 移动端顶部栏
  .mobile-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: #fff;
    border-bottom: 1px solid #ebeef5;
    position: sticky;
    top: 0;
    z-index: 100;

    .header-title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .new-btn {
      border-radius: 20px;
      padding: 8px 16px;
    }
  }

  // 移动端搜索栏
  .mobile-search {
    padding: 12px 16px;
    background: #fff;
    border-bottom: 1px solid #ebeef5;

    .el-input {
      border-radius: 20px;
    }
  }

  // 移动端横幅
  .mobile-banner {
    margin: 16px;
    padding: 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: #fff;
    text-align: center;
    cursor: pointer;

    .banner-content {
      .banner-icon {
        font-size: 48px;
        margin-bottom: 12px;
        opacity: 0.9;
      }

      .banner-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 8px;
      }

      .banner-desc {
        font-size: 14px;
        opacity: 0.8;
      }
    }
  }
  // 移动端内容区域
  .mobile-content {
    flex: 1;

    .scroll-content {
      height: 100%;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
      padding: 0 16px 16px;
    }

    .mobile-list {
      .mobile-card {
        background: #fff;
        border-radius: 12px;
        margin-bottom: 12px;
        padding: 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        cursor: pointer;
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.98);
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        }

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 12px;

          .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            line-height: 1.4;
            flex: 1;
            margin-right: 12px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }

          .card-time {
            font-size: 12px;
            color: #909399;
            white-space: nowrap;
          }
        }

        .card-content {
          margin-bottom: 12px;

          .content-preview {
            font-size: 14px;
            color: #606266;
            line-height: 1.5;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
          }
        }

        .card-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .footer-left {
            display: flex;
            align-items: center;
            gap: 8px;

            .author {
              font-size: 12px;
              color: #909399;
            }
          }

          .footer-right {
            display: flex;
            gap: 8px;

            .el-button {
              padding: 4px 8px;
              font-size: 12px;

              &.danger {
                color: #f56c6c;
              }
            }
          }
        }
      }
    }

    // 空状态
    .mobile-empty {
      text-align: center;
      padding: 60px 20px;

      .empty-icon {
        font-size: 64px;
        color: #dcdfe6;
        margin-bottom: 16px;
      }

      .empty-text {
        font-size: 16px;
        color: #909399;
        margin-bottom: 24px;
      }
    }

    // 加载更多
    .load-more {
      text-align: center;
      padding: 20px;

      .loading-text {
        color: #409eff;
        font-size: 14px;

        i {
          margin-right: 8px;
        }
      }

      .load-text {
        color: #409eff;
        font-size: 14px;
        cursor: pointer;
        padding: 8px 16px;
        border-radius: 20px;
        border: 1px solid #409eff;
        display: inline-block;
        transition: all 0.3s ease;

        &:active {
          background: #409eff;
          color: #fff;
        }
      }

      .no-more-text {
        color: #c0c4cc;
        font-size: 14px;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .mobile-header {
      .header-title {
        font-size: 16px;
      }
    }

    .mobile-content {
      .mobile-card {
        margin-bottom: 8px;
        padding: 12px;

        .card-header {
          .card-title {
            font-size: 15px;
          }

          .card-time {
            font-size: 11px;
          }
        }

        .card-content {
          .content-preview {
            font-size: 13px;
          }
        }
      }
    }
  }
}
</style>
