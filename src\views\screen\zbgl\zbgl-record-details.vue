<style>
.sql-formatter-button {
  float: right;
  margin-top: 6px;
  padding: 8px 12px;
  font-size: 12px;
}
</style>
<template>
  <div>
    <el-dialog
      :title="'记录信息'"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :visible.sync="visible"
      width="60%"
    >
      <el-form
        ref="dataForm"
        :model="dataForm"
        label-width="110px"
        @keyup.enter.native=""
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="指标编码" prop="bm">
              <el-input v-model="dataForm.bm" :readonly="true"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="指标领域" prop="mch1">
              <el-select
                v-model="dataForm.mch1"
                :disabled="true"
                placeholder="请选择指标领域（专题页面）"
                style="width: 100%"
              >
                <el-option
                  v-for="item in this.ymzts"
                  :key="item.dataCode"
                  :label="item.dataName"
                  :value="item.dataCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="指标概述" prop="mch2">
              <el-input v-model="dataForm.mch2" :readonly="true"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据来源" prop="sjly">
              <el-select
                v-model="dataForm.sjly"
                :disabled="true"
                placeholder="数据来源 "
                style="width: 100%"
                @change="sjlyOnChange('')"
              >
                <el-option
                  v-for="item in sjlys"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="展示方式" prop="mch3">
              <el-select
                v-model="dataForm.mch3"
                :disabled="true"
                placeholder="选择展示方式 "
                style="width: 100%"
              >
                <el-option
                  v-for="item in this.stypes"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="返回格式" prop="mch4">
              <el-select
                v-model="dataForm.mch4"
                :disabled="true"
                placeholder="选择返回格式 "
                style="width: 100%"
              >
                <el-option
                  v-for="item in this.types"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请求方式" prop="qqfs">
              <el-select
                v-model="dataForm.qqfs"
                :disabled="true"
                placeholder="请求方式 "
                style="width: 100%"
              >
                <el-option
                  v-for="item in this.qqfss"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="指标类" prop="zbl">
              <treeselect
                v-model="dataForm.zbl"
                :disabled="true"
                :options="indexOptions"
                :normalizer="normalizer"
                placeholder="请选择指标类"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="指标项" prop="zbx">
              <el-checkbox-group v-model="dataForm.zbx">
                <el-checkbox
                  v-for="dict in zbxOptions"
                  :key="dict.indexId"
                  :disabled="true"
                  :label="dict.indexId"
                >
                  {{ dict.indexName }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item v-show="jkdz_ele_show" label="接口地址" prop="jkdz">
              <el-input
                v-model="dataForm.jkdz"
                :readonly="true"
                type="textarea"
                :autosize="{ minRows: 1, maxRows: 3 }"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-show="mmkj_ele_show" label="命名空间" prop="mmkj">
              <el-input v-model="dataForm.mmkj" :readonly="true"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-show="ffm_ele_show" label="方法名" prop="ffm">
              <el-input v-model="dataForm.ffm" :readonly="true"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="指标参数" prop="zbcs">
          <div v-for="(item, index) in dataForm.zbcs">
            <el-row>
              <el-col :span="4">
                <el-form-item :prop="'zbcs.' + index + '.csm'">
                  <el-input
                    v-model="item.csm"
                    :readonly="true"
                    size="small"
                    style="width: 120px"
                    placeholder="参数名称"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item :prop="'zbcs.' + index + '.csz'">
                  <el-input
                    v-model="item.csz"
                    :readonly="true"
                    size="small"
                    style="width: 120px"
                    placeholder="初始值"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :prop="'zbcs.' + index + '.csms'">
                  <el-input
                    v-model="item.csms"
                    :readonly="true"
                    size="small"
                    style="width: 360px"
                    placeholder="参数描述"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item
                  label=""
                  size="mini"
                  :prop="'zbcs.' + index + '.sfbt'"
                >
                  <el-checkbox-group v-model="item.sfbt">
                    <el-checkbox :readonly="true" :label="1">
                      参数必传
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form-item>
        <el-form-item v-show="key_ele_show" label="请求头参数" prop="hdcs">
          <div v-for="(item, index) in dataForm.hdcs">
            <el-row>
              <el-col :span="8">
                <el-form-item :prop="'hdcs.' + index + '.keylabel'">
                  <el-input
                    v-model="item.keylabel"
                    :readonly="true"
                    size="small"
                    style="width: 88%"
                    placeholder="key"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :prop="'hdcs.' + index + '.keyval'">
                  <el-input
                    v-model="item.keyval"
                    :readonly="true"
                    size="small"
                    style="width: 88%"
                    placeholder="value"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form-item>
        <el-form-item v-show="cdzd_ele_show" label="状态码字段名">
          <el-col :span="13">
            <el-form-item prop="cdzd">
              <el-input
                v-model="dataForm.cdzd"
                :readonly="true"
                style="width: 95%"
                placeholder="状态码字段名,请根据接口文档返回参数填写(如：code、responseCode)"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item prop="cdz">
              <el-input
                v-model="dataForm.cdz"
                :readonly="true"
                placeholder="正常返回的状态码,请根据接口文档返回参数填写(如：200、true)"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item v-show="msgzd_ele_show" label="信息字段名" prop="msgzd">
          <el-input
            v-model="dataForm.msgzd"
            :readonly="true"
            placeholder="请根据对方提供的接口文档返回参数填写。（如：message、msg）"
          ></el-input>
        </el-form-item>
        <el-form-item v-show="sjzd_ele_show" label="数据字段名" prop="sjzd">
          <el-input
            v-model="dataForm.sjzd"
            :readonly="true"
            placeholder="请根据第三方提供的接口文档返回参数填写"
          ></el-input>
        </el-form-item>
        <el-form-item v-show="cssc_ele_show" label="超时限制(ms)" prop="cssc">
          <el-input v-model="dataForm.cssc" :readonly="true"></el-input>
        </el-form-item>
        <el-form-item v-show="sql_ele_show" label="完全公开SQL" prop="sqla">
          <el-input
            v-model="dataForm.sqla"
            :readonly="true"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 8 }"
          ></el-input>
          <!--          <el-button type="primary" @click="sqlFormatter(dataForm.sqla,1)" class="sql-formatter-button">美化SQL-->
          <!--          </el-button>-->
        </el-form-item>
        <el-form-item v-show="false" label="半公开SQL" prop="sqlb">
          <el-input
            v-model="dataForm.sqlb"
            :readonly="true"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 8 }"
          ></el-input>
          <!--          <el-button type="primary" @click="sqlFormatter(dataForm.sqlb,2)" class="sql-formatter-button">美化SQL-->
          <!--          </el-button>-->
        </el-form-item>
        <el-form-item v-show="false" label="不公开SQL" prop="sqlc">
          <el-input
            v-model="dataForm.sqlc"
            :readonly="true"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 8 }"
          ></el-input>
          <!--          <el-button type="primary" @click="sqlFormatter(dataForm.sqlc,3)" class="sql-formatter-button">美化SQL-->
          <!--          </el-button>-->
        </el-form-item>
        <el-form-item label="录入说明" prop="lrsm">
          <el-input
            v-model="dataForm.lrsm"
            :readonly="true"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 8 }"
          ></el-input>
        </el-form-item>
        <el-form-item v-show="xgsmIsShow" label="修改说明" prop="xgsm">
          <el-input
            v-model="dataForm.xgsm"
            :readonly="true"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 8 }"
          ></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="bz">
          <el-input
            v-model="dataForm.bz"
            :readonly="true"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 8 }"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button
          v-show="debug_but_ele_show"
          type="primary"
          style="background-color: #07d607; border-color: #07d607"
          @click="debugScript()"
        >
          调试
        </el-button>
        <el-button @click="visible = false">取消</el-button>
      </span>
    </el-dialog>
    <!-- 弹窗-->
    <zb-debug v-if="zbDebugVisible" ref="zbDebug"></zb-debug>
  </div>
</template>

<script>
import { listIndex, getIndexItemList } from '@/api/screen/index'
import { getZbRecordData, getDpztList, debugScript } from '@/api/screen/zbgl'
import zbglDebug from './zbgl-debug.vue'
import * as sqlFormatter from 'sql-formatter'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { Loading } from 'element-ui'

export default {
  components: {
    zbDebug: zbglDebug,
    Treeselect,
  },
  data() {
    return {
      visible: false,
      dataForm: {
        id: '',
        bm: '',
        mch1: '',
        mch2: '',
        mch3: '1',
        mch4: '1',
        zbl: '0',
        zbx: [],
        sqla: '',
        sqlb: '',
        sqlc: '',
        lrsm: '',
        xgsm: '',
        bz: '',
        sjly: '1',
        qqfs: '1',
        jkdz: '',
        mmkj: '',
        ffm: '',
        cssc: '30000',
        sjzd: '',
        cdzd: '',
        cdz: '',
        msgzd: '',
        hdcs: [
          {
            keylabel: '',
            keyval: '',
          },
        ],
        zbcs: [
          {
            csm: '',
            csz: '',
            sfbt: false,
            csms: '',
          },
        ],
      },
      resetDataForm: {
        id: '',
        bm: '',
        mch1: '',
        mch2: '',
        mch3: '1',
        mch4: '1',
        zbl: '0',
        zbx: [],
        sqla: '',
        sqlb: '',
        sqlc: '',
        lrsm: '',
        xgsm: '',
        bz: '',
        sjly: '1',
        qqfs: '1',
        jkdz: '',
        mmkj: '',
        ffm: '',
        cssc: '30000',
        sjzd: '',
        cdzd: '',
        cdz: '',
        msgzd: '',
        hdcs: [
          {
            keylabel: '',
            keyval: '',
          },
        ],
        zbcs: [
          {
            csm: '',
            csz: '',
            sfbt: false,
            csms: '',
          },
        ],
      },
      stypes: [
        {
          dictLabel: '',
          dictValue: '',
        },
      ],
      types: [
        {
          dictLabel: '',
          dictValue: '',
        },
      ],
      sjlys: [
        {
          dictLabel: '',
          dictValue: '',
        },
      ],
      qqfss: [
        {
          dictLabel: '',
          dictValue: '',
        },
      ],
      ymzts: [
        {
          dataName: '',
          dataCode: '',
        },
      ],
      // 指标项树选项
      indexOptions: [],
      // 指标项选项
      zbxOptions: [],
      // 指标类选项变更次数
      zblChangeNum: 0,
      bmReadonly: false, // 编码元素是否只读
      xgsmIsShow: false, // 是否展示修改描述元素
      dataListLoading: false,
      zbDebugVisible: false,
      sql_ele_show: true, // 是否展示sql元素
      jkdz_ele_show: false, // 是否展示接口地址元素
      mmkj_ele_show: false, // 是否展示命名空间元素
      ffm_ele_show: false, // 是否展示方法名元素
      key_ele_show: false, // 是否展示第三方接口请求key元素
      cssc_ele_show: false, // 是否展示第三方接口超时限制元素
      sjzd_ele_show: false, // 是否展示第三方接口返回数据字段名元素
      cdzd_ele_show: false, // 是否展示第三方接口返回状态码字段名元素
      msgzd_ele_show: false, // 是否展示第三方接口返回消息提示字段名元素
      debug_but_ele_show: true, // 是否展示指标调试按钮元素
    }
  },
  watch: {
    'dataForm.zbl': 'zblChange',
  },
  created() {
    this.getTreeselect()
    this.getDicts('screen_dataStyle').then((response) => {
      this.stypes = response.data
    })
    this.getDicts('screen_returnType').then((response) => {
      this.types = response.data
    })
    this.getDicts('screen_dataSource').then((response) => {
      this.sjlys = response.data
    })
    this.getDicts('screen_methTypes').then((response) => {
      this.qqfss = response.data
    })
  },
  methods: {
    // 接收父页面传递的值
    init(id, subType) {
      this.zblChangeNum = 0
      this.visible = true
      this.$nextTick(() => {
        // this.$refs['dataForm'].resetFields()
        this.dataForm = this.resetDataForm
        if (id.length > 0) {
          getZbRecordData({ id: id }).then((response) => {
            if (response && response.successful) {
              this.dataForm = response.data
              const zbcsArr = response.data.zbcs
              if (zbcsArr.length === 0) {
                this.dataForm.zbcs.push({
                  csm: '',
                  csz: '',
                  csms: '',
                  sfbt: false,
                })
              } else {
                for (var i = 0; i < zbcsArr.length; i++) {
                  if (zbcsArr[i].sfbt === 0) {
                    zbcsArr[i].sfbt = false
                  } else {
                    zbcsArr[i].sfbt = true
                  }
                }
                this.dataForm.zbcs = zbcsArr
              }

              const keyArr = response.data.hdcs
              if (keyArr === null || keyArr.length === 0) {
                this.dataForm.hdcs.push({
                  keylabel: '',
                  keyval: '',
                })
              } else {
                this.dataForm.hdcs = keyArr
              }
              if (response.data.zbx != null) {
                const zbxArr = response.data.zbx.split(',')
                for (let i = 0; i < zbxArr.length; i++) {
                  zbxArr[i] = parseInt(zbxArr[i])
                }
                this.dataForm.zbx = zbxArr
              } else {
                this.dataForm.zbx = []
              }
              this.sjlyOnChange(response.data.sjly)
            } else {
              console.log('ERROR', response)
              this.$message.error('操作失败')
            }
          })
          this.bmReadonly = true
          this.xgsmIsShow = true
        } else {
          this.bmReadonly = false
          this.xgsmIsShow = false
          this.sjlyOnChange('')
        }
        this.dataForm.id = id
        this.dataForm.type = subType
        this.getDpztList()
        // this.getStyle()
        // this.getType()
        // this.getSjlys()
        // this.getQqfss()
      })
    },
    /** 转换指标项数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.indexId,
        label: node.indexName,
        children: node.children,
      }
    },
    /** 查询指标下拉树结构 */
    getTreeselect() {
      listIndex({ indexType: 1 }).then((response) => {
        this.indexOptions = []
        const data = { indexId: 0, indexName: '顶级节点', children: [] }
        data.children = this.handleTree(response.data, 'indexId', 'parentId')
        this.indexOptions.push(data)
      })
    },
    // 调试
    debugScript() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.dataListLoading = true
          var params = {
            bm: this.dataForm.bm,
            sjly: this.dataForm.sjly,
          }
          // 库表
          if (this.dataForm.sjly === '1') {
            params.mch3 = this.dataForm.mch3
            params.sqla = this.dataForm.sqla
            // 接口
          } else {
            let keylabel = ''
            let keyval = ''
            const hearderCs = this.dataForm.hdcs
            for (let i = 0; i < hearderCs.length; i++) {
              if (
                hearderCs[i].keylabel.length > 0 &&
                hearderCs[i].keyval.length > 0
              ) {
                if (keylabel.length > 0) {
                  keylabel += ',' + hearderCs[i].keylabel
                  keyval += ',' + hearderCs[i].keyval
                } else {
                  keylabel += hearderCs[i].keylabel
                  keyval += hearderCs[i].keyval
                }
              }
            }
            params.jkdz = this.dataForm.jkdz
            params.qqfs = this.dataForm.qqfs
            params.keylabel = this.dataForm.keylabel
            params.keyval = this.dataForm.keyval
            params.cssc = this.dataForm.cssc
            params.sjzd = this.dataForm.sjzd
            params.mmkj = this.dataForm.mmkj
            params.ffm = this.dataForm.ffm
            params.cdzd = this.dataForm.cdzd
            params.cdz = this.dataForm.cdz
            params.msgzd = this.dataForm.msgzd
            params.keylabel = keylabel
            params.keyval = keyval
          }
          const zbcs = this.dataForm.zbcs
          const zbcsArr = []
          for (let i = 0; i < zbcs.length; i++) {
            if (zbcs[i].csm.length > 0) {
              zbcsArr.push({
                csm: zbcs[i].csm,
                csz: zbcs[i].csz,
              })
            }
          }
          params.zbcs = zbcsArr
          const loadingInstance = Loading.service({
            text: '正在获取调试结果。。。',
          })
          debugScript(params)
            .then((response) => {
              loadingInstance.close()
              this.debugHandle(response.totalTimes, response.data)
            })
            .catch(function(error) {
              loadingInstance.close()
            })
        }
      })
    },
    // 将调试结果传递给弹窗
    debugHandle(totalTimes, tsjg) {
      this.zbDebugVisible = true
      this.$nextTick(() => {
        this.dataListLoading = false
        this.$refs.zbDebug.init(totalTimes, tsjg)
      })
    },
    // 格式化sql脚本
    sqlFormatter(sqlScript, sqlEleType) {
      // 获取格式化SQL
      var formatSql = sqlFormatter.format(sqlScript, { language: 'mysql' })
      if (sqlEleType === 1) {
        this.dataForm.sqla = formatSql
      }
      if (sqlEleType === 2) {
        this.dataForm.sqlb = formatSql
      }
      if (sqlEleType === 3) {
        this.dataForm.sqlc = formatSql
      }
    },
    // 监听数据来源下拉框值改变
    sjlyOnChange(sjly) {
      var sjlyVal = '1'
      if (sjly === null || sjly === '') {
        sjlyVal = this.dataForm.sjly
      } else {
        sjlyVal = sjly
      }
      if (sjlyVal === '1') {
        this.sql_ele_show = true
        this.jkdz_ele_show = false
        this.key_ele_show = false
        this.cssc_ele_show = false
        this.sjzd_ele_show = false
        this.cdzd_ele_show = false
        this.msgzd_ele_show = false
        this.debug_but_ele_show = true
      }
      if (sjlyVal === '2' || sjlyVal === '4') {
        if (sjlyVal === '2') {
          this.mmkj_ele_show = false
          this.ffm_ele_show = false
        } else {
          this.mmkj_ele_show = true
          this.ffm_ele_show = true
        }
        this.sql_ele_show = false
        this.jkdz_ele_show = true
        this.key_ele_show = true
        this.cssc_ele_show = true
        this.sjzd_ele_show = true
        this.cdzd_ele_show = true
        this.msgzd_ele_show = true
        this.debug_but_ele_show = false
      }
      if (sjlyVal === '3' || sjlyVal === '5') {
        if (sjlyVal === '3') {
          this.mmkj_ele_show = false
          this.ffm_ele_show = false
        } else {
          this.mmkj_ele_show = true
          this.ffm_ele_show = true
        }
        this.sql_ele_show = false
        this.jkdz_ele_show = true
        this.key_ele_show = true
        this.cssc_ele_show = true
        this.sjzd_ele_show = true
        this.cdzd_ele_show = false
        this.msgzd_ele_show = false
        this.debug_but_ele_show = false
      }
    },
    // 获取页面专题
    getDpztList() {
      getDpztList({}).then((response) => {
        this.ymzts = response.data
      })
    },
    // 指标类选项变更操作
    zblChange() {
      console.log(this.zblChangeNum)
      // >1排除回填变更当次
      if (this.zblChangeNum > 1) {
        this.dataForm.zbx = []
      }
      getIndexItemList({
        indexId: this.dataForm.zbl == null ? 0 : this.dataForm.zbl,
      }).then((response) => {
        this.zbxOptions = response.data
      })
      this.zblChangeNum++
    },
  },
}
</script>
