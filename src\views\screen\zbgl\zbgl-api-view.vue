<style>
.center-show {
  margin-top: 2%;
  height: 85%;
  display: inline-grid;
  text-align: center;
}
.show-scroll {
  overflow-y: auto;
}
.pdf-print {
  height: auto;
}
</style>
<template>
  <div>
    <el-dialog
      :title="'指标API文档'"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :visible.sync="visible"
      width="60%"
    >
      <el-form :model="dataForm" style="margin-top: -36px">
        <el-form-item>
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleDown()"
            style="float: right"
            >导出</el-button
          >
        </el-form-item>
        <div id="zbapiele">
          <div v-for="(item, index) in dataForm.mdContent" class="api_div_item">
            <el-row>
              <el-form-item :prop="'mdContent.' + index">
                <mavon-editor
                  class="md"
                  :value="item"
                  :subfield="prop.subfield"
                  :defaultOpen="prop.defaultOpen"
                  :toolbarsFlag="prop.toolbarsFlag"
                  :editable="prop.editable"
                  :scrollStyle="prop.scrollStyle"
                ></mavon-editor>
              </el-form-item>
            </el-row>
          </div>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleDown()"
          >导出</el-button
        >
        <el-button size="mini" @click="visible = false">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { exportApiToPdf } from "@/api/screen/zbgl";
import { Loading } from "element-ui";
import Vue from "vue";
import mavonEditor from "mavon-editor";
import "mavon-editor/dist/css/index.css";

// use
Vue.use(mavonEditor);
export default {
  components: {},
  data() {
    return {
      visible: false,
      dataForm: {
        zbnum: 0,
        mdContent: [],
        id: "",
        bm: "",
        mch1: "",
        mch2: "",
      },
    };
  },
  computed: {
    prop() {
      let data = {
        subfield: false, // 单双栏模式
        defaultOpen: "preview", // edit： 默认展示编辑区域 ， preview： 默认展示预览区域
        editable: false,
        toolbarsFlag: false,
        scrollStyle: true,
      };
      return data;
    },
  },
  methods: {
    // 接收父页面传递的值
    init(data, id, bm, mch1, mch2) {
      this.visible = true;
      this.$nextTick(() => {
        // this.$refs['dataForm'].resetFields()
        // this.dataForm.zbnum = data.zbnum
        this.dataForm.mdContent = data;
        this.dataForm.id = id;
        this.dataForm.bm = bm;
        this.dataForm.mch1 = mch1;
        this.dataForm.mch2 = mch2;
      });
    },
    exportApiToPdf() {
      let loadingInstance = Loading.service({
        text: "正在拼命导出中，请稍等...",
      });
      var bm = "";
      var mch1 = "";
      var mch2 = "";
      if (this.dataForm.id.length === 0) {
        bm = this.dataForm.bm;
        mch1 = this.dataForm.mch1;
        mch2 = this.dataForm.mch2;
      }
      var top = 20;
      var wimHeight = document.body.offsetHeight - top * 2;
      var winWidth = 1400;
      var left = (document.body.offsetWidth - winWidth) / 2;
      exportApiToPdf(this.dataForm).then((response) => {
        const blob = new Blob([response]);
        const fileName = "指标 " + this.dataForm.bm + " 接口API文档.pdf";
        const elink = document.createElement("a"); // 创建a标签
        elink.download = fileName; // 为a标签添加download属性
        // a.download = fileName; //命名下载名称
        elink.style.display = "none";
        elink.href = URL.createObjectURL(blob);
        document.body.appendChild(elink);
        elink.click(); // 点击下载
        URL.revokeObjectURL(elink.href); // 释放URL 对象
        document.body.removeChild(elink); // 释放标签
      });
      // window.open(
      //   this.$http.adornUrl('/zbgldc/exportApiToPdf?id=' + this.dataForm.id + '&bm=' + bm + '&mch1=' + mch1 + '&mch2=' + mch2),
      //   '指标API文档',
      //   'height=' + wimHeight + ', width=' + winWidth + ', top=' + top + ', left=' + left + ', toolbar=no, menubar=no, scrollbars=no, resizable=no, location=no, status=no'
      // )
      loadingInstance.close();
    },
    handleDown() {
      // 后端导出PDF
      this.exportApiToPdf();
    },
  },
};
</script>
