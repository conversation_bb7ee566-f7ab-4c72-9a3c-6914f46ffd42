<template>
  <textarea ref="textarea"></textarea>
</template>

<script>
import tinymce from 'tinymce/tinymce';
import 'tinymce/skins/ui/oxide/skin';
import 'tinymce/themes/silver/theme';
import 'tinymce/icons/default/icons';
import 'tinymce/models/dom/model';
import 'tinymce/plugins/advlist';
import 'tinymce/plugins/autolink';
import 'tinymce/plugins/autosave';
import 'tinymce/plugins/code';
import 'tinymce/plugins/directionality';
import 'tinymce/plugins/fullscreen';
import 'tinymce/plugins/insertdatetime';
import 'tinymce/plugins/link';
import 'tinymce/plugins/anchor';
import 'tinymce/plugins/lists';
import 'tinymce/plugins/media';
import 'tinymce/plugins/nonbreaking';
import 'tinymce/plugins/pagebreak';
import 'tinymce/plugins/preview';
import 'tinymce/plugins/searchreplace';
import 'tinymce/plugins/table';
import 'tinymce/plugins/visualblocks';
import 'tinymce/plugins/visualchars';
import 'tinymce/plugins/wordcount';
import 'tinymce/plugins/image';
// 导入字号映射
import { fontSizeWordMap, getQuillSizeWhitelist } from '../../utils/fontSizeMap';
// 导入html-to-docx和file-saver
import HTMLtoDOCX from 'html-to-docx';
import { saveAs } from 'file-saver';
// 导入jQuery和jQuery Word Export插件
import $ from 'jquery';
// 将jQuery和saveAs挂载到全局，供jQuery插件使用
window.jQuery = $;
window.$ = $;
window.saveAs = saveAs;

export default {
  components: {},
  props: {
    value: String,
    // 编辑器高度
    height: {
      type: Number,
      default: 400
    },
    // 最小高度
    minHeight: {
      type: Number,
      default: null
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否显示导出按钮
    showExport: {
      type: Boolean,
      default: true
    },
    // 导出文件名
    exportFilename: {
      type: String,
      default: '文档.docx'
    },
    // 工具栏配置
    toolbar: {
      type: String,
      default: null
    },
    // 插件配置
    plugins: {
      type: String,
      default: null
    },
    // 是否保留原始HTML（用于粘贴图片阻止等）
    preserveRawHtml: {
      type: Boolean,
      default: false
    },
    // 导出方式：'html-to-docx'、'jquery-plugin' 或 'both'
    exportMethod: {
      type: String,
      default: 'html-to-docx',
      validator: (value) => ['html-to-docx', 'jquery-plugin', 'both'].includes(value)
    },
    // 是否启用自动滚动到底部（用于流式输出）
    autoScroll: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      editor: null,
      // 自定义字体列表
      fonts: [
        'SimSun',
        'SimHei',
        'Microsoft-YaHei',
        'KaiTi_GB2312',
        'FangSong_GB2312',
        'FangZheng_XiaoBiaoSong',
      ],
      // 自定义字号列表
      fontSizes: getQuillSizeWhitelist(),
    };
  },
  watch: {
    value: {
      handler() {
        this.setValue(this.value);
      },
      immediate: true,
    },
  },
  async mounted() {
    // 动态加载jQuery插件
    try {
      await import('@/assets/js/jquery.wordexport.js');
      console.log('jQuery wordExport插件加载成功');
    } catch (error) {
      console.error('jQuery wordExport插件加载失败:', error);
    }

    // 调试：检查jQuery插件加载状态
    console.log('jQuery版本:', $.fn.jquery);
    console.log('saveAs是否可用:', typeof window.saveAs);
    console.log('wordExport插件是否可用:', typeof $.fn.wordExport);

    const editorConfig = {
      target: this.$refs.textarea,
      ui_mode: 'split',
      content_css: '/tinymce/skins/ui/oxide/content.css',
      language_url: '/tinymce/langs/zh_CN.js',
      language: 'zh_CN',
      plugins: this.plugins || '',
      toolbar: this.getToolbarConfig(),
      // 自定义字体配置
      font_family_formats: this.getFontFamilyFormats(),
      // 自定义字号配置
      font_size_formats: this.getFontSizeFormats(),
      font_size_input_default_unit: 'px',
      images_upload_handler: (blobInfo, progress) => {
        return this.uploadImage(blobInfo, progress);
      },
      height: this.height || this.minHeight || 400,
      min_height: this.minHeight,
      readonly: this.disabled,
      promotion: false,
      menubar: false,
      // 自定义按钮
      setup: (editor) => {
        this.setupCustomButtons(editor);
      },
      // 初始化完成回调
      init_instance_callback: (editor) => {
        this.editor = editor;
        this.setValue(this.value);

        // 绑定事件
        editor.on('change keyup undo redo', () => {
          const content = editor.getContent();
          this.$emit('input', content);
          this.$emit('on-change', {
            html: content,
            text: editor.getContent({ format: 'text' }),
            editor: editor
          });
        });

        editor.on('focus', (event) => {
          this.$emit('on-focus', event, editor);
        });

        editor.on('blur', (event) => {
          this.$emit('on-blur', event, editor);
        });

        // 处理粘贴图片阻止
        if (this.preserveRawHtml) {
          editor.on('paste', (event) => {
            const clipboardData = event.clipboardData || window.clipboardData;
            if (clipboardData && clipboardData.files && clipboardData.files.length > 0) {
              // 检查是否有图片文件
              for (let i = 0; i < clipboardData.files.length; i++) {
                const file = clipboardData.files[i];
                if (file.type.startsWith('image/')) {
                  event.preventDefault();
                  this.$emit('on-paste-image-blocked', '禁止粘贴图片，请使用插入图片功能');
                  return false;
                }
              }
            }
          });
        }

        this.$emit('on-init', editor);
      }
    };

    tinymce.init(editorConfig);
  },
  beforeDestroy() {
    this.editor?.destroy();
  },
  methods: {
    // 获取工具栏配置
    getToolbarConfig() {
      if (this.toolbar) {
        return this.toolbar;
      }

      let defaultToolbar = 'undo redo removeformat bold italic underline strikethrough superscript subscript forecolor numlist bullist blocks fontfamily fontsize fontsizeselect alignleft aligncenter alignright alignjustify lineheight';

      // 如果显示导出按钮，添加到工具栏
      if (this.showExport) {
        if (this.exportMethod === 'html-to-docx') {
          defaultToolbar += ' | exportword';
        } else if (this.exportMethod === 'jquery-plugin') {
          defaultToolbar += ' | exportwordjquery';
        } else if (this.exportMethod === 'both') {
          // 同时显示两种导出方式
          defaultToolbar += ' | exportword exportwordjquery';
        }
      }

      return defaultToolbar;
    },

    // 设置自定义按钮
    setupCustomButtons(editor) {
      if (this.showExport) {
        // 注册html-to-docx导出Word按钮
        if (this.exportMethod === 'html-to-docx' || this.exportMethod === 'both') {
          editor.ui.registry.addButton('exportword', {
            text: '导出Word',
            icon: 'export',
            tooltip: '导出为Word文档(html-to-docx)',
            onAction: () => {
              this.exportToWord();
            }
          });
        }

        // 注册jQuery插件导出Word按钮
        if (this.exportMethod === 'jquery-plugin' || this.exportMethod === 'both') {
          editor.ui.registry.addButton('exportwordjquery', {
            text: '导出为Word',
            icon: 'export',
            tooltip: '导出为Word文档',
            onAction: () => {
              this.exportToWordJQuery();
            }
          });
        }
      }
    },

    // 生成字体族格式字符串
    getFontFamilyFormats() {
      const fontFormats = this.fonts.map(font => {
        const displayName = this.getFontDisplayName(font);
        return `${displayName}=${font}`;
      });
      return fontFormats.join(';');
    },

    // 获取字体显示名称
    getFontDisplayName(fontFamily) {
      const fontMap = {
        'SimSun': '宋体',
        'SimHei': '黑体',
        'Microsoft-YaHei': '微软雅黑',
        'KaiTi_GB2312': '楷体',
        'FangSong_GB2312': '仿宋',
        'FangZheng_XiaoBiaoSong': '方正小标宋'
      };
      return fontMap[fontFamily] || fontFamily;
    },

    // 生成字号格式字符串
    getFontSizeFormats() {
      const sizeFormats = fontSizeWordMap.map(item => {
        return `${item.title}=${item.value}px`;
      });
      // 添加一些常用的像素字号
      const commonSizes = ['9px', '10px', '11px', '12px', '14px', '16px', '18px', '20px', '22px', '24px', '26px', '28px', '36px', '42px', '48px', '72px'];
      const allSizes = [...sizeFormats, ...commonSizes];
      return allSizes.join(' ');
    },

    setValue(val) {
      const oldValue = this.editor?.getContent();
      if (typeof val === 'string' && val !== oldValue) {
        this.editor?.setContent(val);
        // 设置内容后自动滚动到底部（用于流式输出）
        if (this.autoScroll) {
          this.scrollToBottom();
        }
      }
    },

    // 导出为Word文档
    async exportToWord(filename = this.exportFilename) {
      if (!this.editor) {
        this.$message.error("编辑器未初始化");
        return;
      }

      try {
        // 显示加载状态
        this.$message({
          message: "正在生成Word文档...",
          type: "info",
          duration: 2000
        });

        // 获取编辑器HTML内容
        const htmlContent = this.editor.getContent();

        if (!htmlContent || htmlContent.trim() === '') {
          this.$message.warning("编辑器内容为空，无法导出");
          return;
        }

        // 预处理HTML内容，确保字体样式正确传递
        const processedHtmlContent = this.preprocessHtmlForDocx(htmlContent);

        // 调试：输出处理前后的HTML内容
        console.log('原始HTML内容:', htmlContent);
        console.log('处理后HTML内容:', processedHtmlContent);

        // 使用html-to-docx转换为Word文档
        const docxBlob = await HTMLtoDOCX(processedHtmlContent, {
          orientation: 'portrait',
          margins: {
            top: 720,    // 1英寸 = 1440 twips，这里设置0.5英寸
            right: 720,
            bottom: 720,
            left: 720
          },
          font: 'SimSun',      // 默认字体设置为宋体
          fontSize: 12,        // 默认字号
          // 启用字体处理
          decodeUnicode: true,
          // 表格选项
          table: {
            row: {
              cantSplit: false  // 允许表格行跨页
            }
          },
          // 页面设置
          title: '导出文档',
          creator: 'TinyEditor'
        });

        // 确保文件名有正确的扩展名
        const finalFilename = filename.endsWith('.docx') ? filename : `${filename}.docx`;

        // 下载文件
        saveAs(docxBlob, finalFilename);

        this.$message.success("Word文档导出成功！");

        // 触发导出成功事件
        this.$emit("on-export", {
          filename: finalFilename,
          content: htmlContent,
          text: this.editor.getContent({ format: 'text' }),
          success: true,
          method: 'html-to-docx'
        });

      } catch (error) {
        console.error("导出Word失败:", error);
        this.$message.error("导出失败：" + (error.message || "未知错误"));

        // 触发导出失败事件
        this.$emit("on-export", {
          filename: filename,
          content: this.editor ? this.editor.getContent() : '',
          text: this.editor ? this.editor.getContent({ format: 'text' }) : '',
          success: false,
          error: error.message || "未知错误",
          method: 'html-to-docx'
        });
      }
    },

    // 使用jQuery插件导出为Word文档
    exportToWordJQuery(filename = this.exportFilename) {
      if (!this.editor) {
        this.$message.error("编辑器未初始化");
        return;
      }

      try {
        // 显示加载状态
        this.$message({
          message: "正在生成Word文档...",
          type: "info",
          duration: 2000
        });

        // 获取编辑器HTML内容
        const htmlContent = this.editor.getContent();

        if (!htmlContent || htmlContent.trim() === '') {
          this.$message.warning("编辑器内容为空，无法导出");
          return;
        }

        // 确保文件名没有扩展名（jQuery插件会自动添加.doc）
        const finalFilename = filename.replace(/\.(docx?|doc)$/i, '');

        // 使用延时确保jQuery插件已加载
        setTimeout(() => {
          try {
            // 检查jQuery插件是否可用
            if (typeof $.fn.wordExport !== 'function') {
              throw new Error('jQuery wordExport 插件未正确加载，请检查插件文件');
            }

            // 创建一个临时的DOM元素来包含内容
            const tempDiv = $('<div>').html(htmlContent).css({
              position: 'absolute',
              left: '-9999px',
              top: '-9999px',
              width: '800px'
            });

            // 临时添加到页面中（jQuery插件需要元素在DOM中）
            $('body').append(tempDiv);

            // 使用jQuery插件导出
            tempDiv.wordExport(finalFilename);

            // 延时移除临时元素
            setTimeout(() => {
              tempDiv.remove();
            }, 1000);

            this.$message.success("Word文档导出成功！");

            // 触发导出成功事件
            this.$emit("on-export", {
              filename: finalFilename + '.doc',
              content: htmlContent,
              text: this.editor.getContent({ format: 'text' }),
              success: true,
              method: 'jquery-plugin'
            });

          } catch (innerError) {
            console.error("jQuery插件导出Word失败:", innerError);
            this.$message.error("导出失败：" + (innerError.message || "未知错误"));

            // 触发导出失败事件
            this.$emit("on-export", {
              filename: filename,
              content: htmlContent,
              text: this.editor.getContent({ format: 'text' }),
              success: false,
              error: innerError.message || "未知错误",
              method: 'jquery-plugin'
            });
          }
        }, 300);

      } catch (error) {
        console.error("jQuery插件导出Word失败:", error);
        this.$message.error("导出失败：" + (error.message || "未知错误"));

        // 触发导出失败事件
        this.$emit("on-export", {
          filename: filename,
          content: this.editor ? this.editor.getContent() : '',
          text: this.editor ? this.editor.getContent({ format: 'text' }) : '',
          success: false,
          error: error.message || "未知错误",
          method: 'jquery-plugin'
        });
      }
    },

    // 预处理HTML内容以确保字体样式正确传递到Word文档
    preprocessHtmlForDocx(htmlContent) {
      // 创建一个临时DOM元素来处理HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = htmlContent;

      // 递归处理所有元素
      this.processElementForDocx(tempDiv);

      return tempDiv.innerHTML;
    },

    // 处理单个元素的字体样式
    processElementForDocx(element) {
      if (element.nodeType === Node.ELEMENT_NODE) {
        // 处理内联样式
        if (element.style && element.style.fontFamily) {
          // 确保字体名称格式正确
          let fontFamily = element.style.fontFamily;

          // 移除引号
          fontFamily = fontFamily.replace(/['"]/g, '');

          // 映射TinyMCE字体名称到Word兼容的字体名称
          const fontMap = {
            'Microsoft-YaHei': 'Microsoft YaHei',
            'KaiTi_GB2312': 'KaiTi',
            'FangSong_GB2312': 'FangSong',
            'FangZheng_XiaoBiaoSong': 'FangSong',  // 方正小标宋在Word中使用仿宋替代
            'SimSun': 'SimSun',
            'SimHei': 'SimHei'
          };

          if (fontMap[fontFamily]) {
            element.style.fontFamily = fontMap[fontFamily];
          } else {
            element.style.fontFamily = fontFamily;
          }
        }

        // 处理font-face属性（如果存在）
        if (element.hasAttribute('face')) {
          const face = element.getAttribute('face');
          const fontMap = {
            'Microsoft-YaHei': 'Microsoft YaHei',
            'KaiTi_GB2312': 'KaiTi',
            'FangSong_GB2312': 'FangSong',
            'FangZheng_XiaoBiaoSong': 'FangSong',
            'SimSun': 'SimSun',
            'SimHei': 'SimHei'
          };

          if (fontMap[face]) {
            element.setAttribute('face', fontMap[face]);
          }
        }

        // 递归处理子元素
        for (let i = 0; i < element.children.length; i++) {
          this.processElementForDocx(element.children[i]);
        }
      }
    },

    // 获取编辑器实例
    getEditor() {
      return this.editor;
    },

    // 获取内容
    getContent(format = 'html') {
      if (!this.editor) return '';
      return this.editor.getContent({ format });
    },

    // 设置内容
    setContent(content) {
      if (this.editor) {
        this.editor.setContent(content);
      }
    },

    // 插入内容
    insertContent(content) {
      if (this.editor) {
        this.editor.insertContent(content);
        // 插入内容后自动滚动到底部
        if (this.autoScroll) {
          this.scrollToBottom();
        }
      }
    },

    // 滚动到编辑器底部
    scrollToBottom() {
      if (this.editor) {
        this.$nextTick(() => {
          try {
            // 方法1: 使用TinyMCE的API滚动到底部
            const body = this.editor.getBody();
            if (body) {
              // 将光标移动到最后
              this.editor.selection.select(body, true);
              this.editor.selection.collapse(false);

              // 滚动到光标位置
              this.editor.selection.scrollIntoView();

              // 额外的滚动确保到底部
              setTimeout(() => {
                if (body.scrollHeight > body.clientHeight) {
                  body.scrollTop = body.scrollHeight;
                }
              }, 50);
            }

            // 方法2: 直接操作iframe（备用方案）
            const iframe = this.editor.getContainer().querySelector('iframe');
            if (iframe && iframe.contentWindow) {
              const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
              const iframeBody = iframeDoc.body;
              if (iframeBody) {
                iframeBody.scrollTop = iframeBody.scrollHeight;
                // 也尝试滚动documentElement
                if (iframeDoc.documentElement) {
                  iframeDoc.documentElement.scrollTop = iframeDoc.documentElement.scrollHeight;
                }
              }
            }
          } catch (error) {
            console.warn('自动滚动到底部失败:', error);
          }
        });
      }
    },

    // 追加内容（用于流式输出）
    appendContent(content) {
      if (this.editor) {
        // 获取当前内容
        const currentContent = this.editor.getContent();
        // 追加新内容
        this.editor.setContent(currentContent + content);
        // 自动滚动到底部
        if (this.autoScroll) {
          this.scrollToBottom();
        }
      }
    },
    uploadImage(blobInfo, progress) {
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.withCredentials = false;
        xhr.open('POST', '/upload');

        xhr.upload.onprogress = (e) => {
          progress((e.loaded / e.total) * 100);
        };

        xhr.onload = () => {
          if (xhr.status === 403) {
            reject({ message: 'HTTP Error: ' + xhr.status, remove: true });
            return;
          }

          if (xhr.status < 200 || xhr.status >= 300) {
            reject('HTTP Error: ' + xhr.status);
            return;
          }

          if (xhr.response.code !== 200) {
            reject('Error: ' + xhr.response.message);
            return;
          }

          resolve(xhr.response.data.list[0].file_url);
        };

        xhr.onerror = () => {
          reject(
            'Image upload failed due to a XHR Transport error. Code: ' +
              xhr.status,
          );
        };
        xhr.setRequestHeader('Authorization', this.$cookie.get('Admin-Token'));

        const formData = new FormData();
        formData.append('file', blobInfo.blob(), blobInfo.filename());
        formData.append('module', 'public');
        formData.append('file_type', 'image');

        xhr.responseType = 'json';
        xhr.send(formData);
      });
    },
  },
};
</script>

<style>
.tox-tinymce-aux {
  z-index: 10000 !important;
}
.tox .tox-number-input .tox-input-wrapper {
  display: none !important;
}
.tox-statusbar__branding {
  display: none !important;
}

/* 自定义字体样式 */
.mce-content-body {
  font-family: "SimSun", serif;
}

/* 宋体 */
.mce-content-body[style*="font-family: SimSun"],
.mce-content-body *[style*="font-family: SimSun"] {
  font-family: "SimSun", serif !important;
}

/* 黑体 */
.mce-content-body[style*="font-family: SimHei"],
.mce-content-body *[style*="font-family: SimHei"] {
  font-family: "SimHei" !important;
}

/* 微软雅黑 */
.mce-content-body[style*="font-family: Microsoft-YaHei"],
.mce-content-body *[style*="font-family: Microsoft-YaHei"] {
  font-family: "Microsoft YaHei" !important;
}

/* 楷体 */
.mce-content-body[style*="font-family: KaiTi_GB2312"],
.mce-content-body *[style*="font-family: KaiTi_GB2312"] {
  font-family: "KaiTi_GB2312", serif !important;
}

/* 仿宋 */
.mce-content-body[style*="font-family: FangSong_GB2312"],
.mce-content-body *[style*="font-family: FangSong_GB2312"] {
  font-family: "FangSong_GB2312", serif !important;
}

/* 方正小标宋 */
.mce-content-body[style*="font-family: FangZheng_XiaoBiaoSong"],
.mce-content-body *[style*="font-family: FangZheng_XiaoBiaoSong"] {
  font-family: "FangZheng_XiaoBiaoSong", serif !important;
}
</style>
