import request from '@/utils/request'

// 查询dwd数据资源管理列表
export function listManage(query) {
  return request({
    url: '/system/dwdTableManage/list',
    method: 'get',
    params: query,
  })
}

// 查询dwd数据资源管理详细
export function getManage(id) {
  return request({
    url: '/system/dwdTableManage/' + id,
    method: 'get',
  })
}

// 新增dwd数据资源管理
export function addManage(data) {
  return request({
    url: '/system/dwdTableManage',
    method: 'post',
    data: data,
  })
}

// 修改dwd数据资源管理
export function updateManage(data) {
  return request({
    url: '/system/dwdTableManage',
    method: 'put',
    data: data,
  })
}

// 删除dwd数据资源管理
export function delManage(id) {
  return request({
    url: '/system/dwdTableManage/' + id,
    method: 'delete',
  })
}

// 数源单位列表
export function getDataSourceDept(keywords) {
  return request({
    url: `/system/dwdTableManage/department?keywords=${keywords}`,
    method: 'get',
  })
}

// 批量修改dwd表
export function batchUpdateDwd(data) {
  return request({
    url: '/system/dwdTableManage/batchUpdate',
    method: 'post',
    data: data,
  })
}

// 一键生成表
export function generateTable(data) {
  return request({
    url: '/system/dwdTableManage/generate',
    method: 'post',
    data: data,
  })
}

// 查询清洗管理详情
export function getCleanDetail(id) {
  return request({
    url: '/system/dwdTableManage/clean/' + id,
    method: 'get',
  })
}

// 清洗管理新增
export function postClean(data) {
  return request({
    url: '/system/dwdTableManage/clean',
    method: 'post',
    data,
  })
}

// ods表名搜索
export function findOdsTable(data) {
  return request({
    url: '/system/dwdTableManage/findOdsTable',
    method: 'post',
    data,
  })
}

// ods来源字段搜索
export function findOdsField(data) {
  return request({
    url: '/system/dwdTableManage/findOdsField',
    method: 'post',
    data,
  })
}

// 搜索维度字典名称
export function findTitleManage(data) {
  return request({
    url: '/system/dwdTableManage/findTitleManage',
    method: 'post',
    data,
  })
}

// 搜索维度字典值
export function findTitleValue(data) {
  return request({
    url: '/system/dwdTableManage/findTitleValue',
    method: 'post',
    data,
  })
}

// 搜索dim关联字段
export function findRelatedField(data) {
  return request({
    url: '/system/dwdTableManage/findRelatedField',
    method: 'post',
    data,
  })
}

// 保存关联管理
export function createRelated(data) {
  return request({
    url: '/system/dwdTableManage/createRelated',
    method: 'post',
    data,
  })
}

// 查询关联管理
export function getListRelated(id) {
  return request({
    url: `/system/dwdTableManage/listRelated/${id}'`,
    method: 'get',
  })
}
