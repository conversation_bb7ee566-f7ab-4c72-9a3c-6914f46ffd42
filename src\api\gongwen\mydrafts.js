import request from "@/utils/request";
import axios from "axios";
import { getToken } from "@/utils/auth";

// 智能体写作 Controller（alapi）
// 查询智能体写作列表
export function listZntXz(query) {
  return request({
    url: "/anlu/tZntXz/list",
    method: "get",
    params: query,
  });
}

// 导出智能体写作列表
export function exportZntXz(query) {
  return request({
    url: "/anlu/tZntXz/export",
    method: "get",
    params: query,
  });
}

// 获取智能体写作详细信息
export function getZntXz(id) {
  return request({
    url: `/anlu/tZntXz/${id}`,
    method: "get",
  });
}

// 新增智能体写作
export function addZntXz(data) {
  return request({
    url: "/anlu/tZntXz/add",
    method: "post",
    data,
  });
}

// 修改智能体写作
export function updateZntXz(data) {
  return request({
    url: "/anlu/tZntXz/edit",
    method: "post",
    data,
  });
}

// 删除智能体写作（支持数组或逗号分隔字符串）
export function delZntXz(ids) {
  const pathIds = Array.isArray(ids) ? ids.join(",") : ids;
  return request({
    url: `/anlu/tZntXz/remove/${pathIds}`,
    method: "post",
  });
}

// 根据ID生成Word并下载
export function downloadZntXzWord(id) {
  // 创建一个新的axios实例，避免全局拦截器的影响
  const downloadRequest = axios.create({
    baseURL: process.env.VUE_APP_BASE_API,
    timeout: 120000, // 2分钟超时
    responseType: "blob",
  });

  // 添加请求拦截器，只处理认证
  downloadRequest.interceptors.request.use((config) => {
    const token = getToken();
    if (token) {
      config.headers["Authorization"] = "Bearer " + token;
    }
    return config;
  });

  // 添加响应拦截器，直接返回blob数据
  downloadRequest.interceptors.response.use(
    (response) => {
      return response.data;
    },
    (error) => {
      console.error("下载请求失败:", error);
      return Promise.reject(error);
    }
  );

  return downloadRequest({
    url: "/anlu/tZntXz/downloadWord",
    method: "get",
    params: { id },
    headers: {
      Accept:
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "Cache-Control": "no-cache",
      Pragma: "no-cache",
    },
  });
}
