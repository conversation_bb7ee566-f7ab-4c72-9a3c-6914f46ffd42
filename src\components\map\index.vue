<template>
    <div class="mapContainer" id="mapInstance" />
</template>

<script>
export default {
  name: 'index',
  data() {
    return {}
  },
  computed: {},
  mounted() {
    this.initMap()
  },
  methods: {
    initMap () {
      window.ArcGisUtils.initSceneView({ divId: 'mapInstance' })
      this.getPoint()
    },
    getPoint() {
      const that = this;
      const clock = setInterval(function () {
        if (window?.view) {
          clearInterval(clock);
          window.ArcGisUtils.mapClickEventHandle.addCoordinateListener((point) => {
            that.$emit("mapClick",point)
          });
        }
      }, 1000);
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
.mapContainer {
  width: 100%;
  height: 100%;
  z-index: 1;
  background-color: #052c4d;
}
</style>
