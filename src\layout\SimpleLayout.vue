<template>
  <div class="simple-layout">
    <!-- 可选的顶部工具栏 -->
    <div v-if="showToolbar" class="simple-toolbar">
      <div class="toolbar-left">
        <el-button
          v-if="showBackButton"
          type="text"
          icon="el-icon-arrow-left"
          @click="goBack"
        >
          返回
        </el-button>
        <span v-if="pageTitle" class="page-title">{{ pageTitle }}</span>
      </div>
      <div class="toolbar-right">
        <slot name="toolbar"></slot>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="simple-main" :class="{ 'with-toolbar': showToolbar }">
      <transition name="fade-transform" mode="out-in">
        <keep-alive :include="cachedViews">
          <router-view :key="key" />
        </keep-alive>
      </transition>
    </div>
  </div>
</template>

<script>
export default {
  name: "SimpleLayout",
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews;
    },
    key() {
      return this.$route.path;
    },
    // 是否显示工具栏
    showToolbar() {
      return this.$route.meta?.showToolbar || false;
    },
    // 是否显示返回按钮
    showBackButton() {
      return this.$route.meta?.showBackButton || false;
    },
    // 页面标题
    pageTitle() {
      return this.$route.meta?.title || "";
    },
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>

<style lang="scss" scoped>
.simple-layout {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .simple-toolbar {
    height: 50px;
    background: #fff;
    border-bottom: 1px solid #e6e6e6;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    flex-shrink: 0;

    .toolbar-left {
      display: flex;
      align-items: center;

      .page-title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
        margin-left: 10px;
      }
    }

    .toolbar-right {
      display: flex;
      align-items: center;
    }
  }

  .simple-main {
    flex: 1;
    overflow: auto;
    background-color: #fff;

    &.with-toolbar {
      height: calc(100vh - 50px);
    }
  }
}

// 过渡动画
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all 0.3s ease;
}

.fade-transform-enter {
  opacity: 0;
  transform: translateX(-20px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(20px);
}
</style>
