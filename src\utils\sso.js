import { getQueryObject } from "@/utils/index";

/**
 * 检查URL中是否包含ticket参数
 * @returns {Object|null} 返回ticket和service信息，如果没有ticket则返回null
 */
export function checkTicketInUrl() {
  const query = getQueryObject(window.location.href);

  if (query.ticket) {
    // 构建service URL（当前页面地址，去除ticket参数）
    const serviceUrl = window.location.origin + window.location.pathname;

    return {
      ticket: query.ticket,
      service: serviceUrl,
    };
  }

  return null;
}

/**
 * 清除URL中的ticket参数
 */
export function cleanTicketFromUrl() {
  const url = new URL(window.location.href);
  url.searchParams.delete("ticket");

  // 使用replaceState避免页面刷新
  window.history.replaceState({}, document.title, url.toString());
}

/**
 * 获取当前页面的service URL
 * @returns {string} service URL
 */
export function getCurrentServiceUrl() {
  return window.location.origin + window.location.pathname;
}
