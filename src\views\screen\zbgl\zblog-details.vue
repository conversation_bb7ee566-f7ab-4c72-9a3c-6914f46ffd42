<template>
  <div>
    <el-dialog
      :title="'日志信息'"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :visible.sync="visible"
      width="60%">
      <el-form :model="dataForm" ref="dataForm"
               @keyup.enter.native=""
               label-width="12%">
        <el-form-item label="调用平台" prop="callername">
          <el-input :readonly="true" v-model="dataForm.callername"></el-input>
        </el-form-item>
        <el-form-item label="指标编码" prop="indexid">
          <el-input :readonly="true" v-model="dataForm.indexid"></el-input>
        </el-form-item>
        <!--<el-form-item label="用户" prop="username">
          <el-input :readonly="true" v-model="dataForm.username"></el-input>
        </el-form-item>-->
        <el-form-item label="客户端ip" prop="clientip">
          <el-input :readonly="true" v-model="dataForm.clientip"></el-input>
        </el-form-item>
        <el-form-item label="调用参数" prop="callparamters">
          <el-input :readonly="true" type="textarea" v-model="dataForm.callparamters"
                    :autosize="{ minRows: 2, maxRows: 8}"></el-input>
        </el-form-item>
        <el-form-item label="调用时间" prop="calldate">
          <el-input :readonly="true" v-model="dataForm.calldate"></el-input>
        </el-form-item>
        <el-form-item label="总时长（ms）" prop="callduration">
          <el-input :readonly="true" v-model="dataForm.callduration"></el-input>
        </el-form-item>
        <el-form-item label="返回状态" prop="callstatus">
          <el-input :readonly="true" v-model="dataForm.callstatus"></el-input>
        </el-form-item>
        <el-form-item label="提示信息" prop="message">
          <el-input :readonly="true" v-model="dataForm.message"></el-input>
        </el-form-item>
        <el-form-item label="返回结果" prop="callresult">
          <el-input :readonly="true" type="textarea" v-model="dataForm.callresult"
                    :autosize="{ minRows: 2, maxRows: 8}"></el-input>
        </el-form-item>
        <el-form-item label="错误信息" prop="error">
          <el-input :readonly="true" type="textarea" v-model="dataForm.error"
                    :autosize="{ minRows: 2, maxRows: 8}"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
    </span>
    </el-dialog>
  </div>
</template>

<script>
  import { getDylog } from "@/api/screen/zbLog"

  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: '',
          callername: '',
          indexid: '',
          //username: '',
          clientip: '',
          callparamters: '',
          calldate: '',
          callduration: '',
          callstatus: '',
          message: '',
          callresult: '',
          error: ''
        }
      }
    },
    created () {
    },
    methods: {
      // 接收父页面传递的值
      init (id) {
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (id.length > 0) {
            getDylog({
              'id': id
            }).then(response=>{
              if (response && response.successful) {
                this.dataForm = response.data
                this.dataForm.callresult = JSON.stringify(JSON.parse(response.data.callresult), null, '\t')
              } else {
                this.$message.error('操作失败')
              }
            })
            // this.$http({
            //   url: this.$http.adornUrl('/dylog/getDylog'),
            //   method: 'get',
            //   params: this.$http.adornParams({
            //     'id': id
            //   })
            // }).then(({data}) => {
            //   if (data && data.successful) {
            //     this.dataForm = data.data
            //     this.dataForm.callresult = JSON.stringify(JSON.parse(data.data.callresult), null, '\t')
            //   } else {
            //     console.log('ERROR', data)
            //     this.$message.error('操作失败')
            //   }
            // })
          }
          this.dataForm.id = id
        })
      }
    }
  }
</script>
