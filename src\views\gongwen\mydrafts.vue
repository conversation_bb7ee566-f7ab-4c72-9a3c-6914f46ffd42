<template>
  <div class="mydrafts-page">
    <!-- 顶部操作与搜索 -->
    <div class="topbar">
      <div class="top-title">我的稿件</div>
      <div class="search">
        <el-input
          v-model="queryParams.keyword"
          clearable
          placeholder="请输入关键字搜索"
          prefix-icon="el-icon-search"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
        />
      </div>
      <div class="actions">
        <el-button type="primary" icon="el-icon-plus" @click="handleNew">
          新建稿件
        </el-button>
        <!-- <el-button type="success" icon="el-icon-download" @click="handleExport">
          导出列表
        </el-button> -->
      </div>
    </div>

    <!-- 标签页切换 -->
    <!-- <div class="tabs-wrapper">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="我的稿件" name="zntxz">
          <template #label>
            <span><i class="el-icon-cpu"></i> 我的稿件</span>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div> -->

    <!-- 横幅引导 -->
    <div class="banner" @click="handleNew">
      <div class="banner-left">
        <div class="banner-title">
          <i class="el-icon-plus plus"></i>
          <span class="text-primary">我的稿件</span>
        </div>
        <div class="banner-desc">
          基于AI智能体的公文写作助手，支持智能生成、内容优化、格式规范等功能，让公文写作更高效
        </div>
        <div class="banner-links">
          <el-link type="primary">快速创建</el-link>
          <span class="dot">·</span>
          <!-- <el-link @click="handleQuery">检索</el-link>
          <span class="dot">·</span>
          <el-link @click="handleExport">导出</el-link> -->
        </div>
      </div>
      <div class="banner-right">
        <i class="el-icon-cpu illus"></i>
      </div>
    </div>

    <!-- 列表区域 -->
    <div class="list-wrapper">
      <el-row :gutter="12" v-if="list && list.length" v-loading="loading">
        <el-col
          v-for="item in list"
          :key="item.id"
          :xs="24"
          :sm="12"
          :md="8"
          :lg="6"
        >
          <el-card class="draft-card" shadow="hover">
            <div class="card-body">
              <div class="card-header">
                <div
                  class="card-title"
                  @click="editDraft(item)"
                  :title="item.title"
                >
                  {{ item.title || "未命名稿件" }}
                </div>
                <el-tag size="small" v-if="item.gwType" type="primary">{{
                  item.gwType
                }}</el-tag>
              </div>

              <div class="card-content" v-if="item.content">
                <div
                  class="content-preview"
                  v-html="getCleanContentPreview(item.content)"
                ></div>
              </div>
              <div class="card-footer">
                <div class="date">
                  {{
                    formatDate(
                      item.createTime ||
                        item.updateTime ||
                        item.cTime ||
                        item.uTime
                    )
                  }}
                </div>
                <div class="ops">
                  <!-- <el-tooltip content="查看" placement="top">
                    <el-button
                      type="text"
                      icon="el-icon-view"
                      @click.stop="viewDraft(item)"
                    />
                  </el-tooltip> -->
                  <el-tooltip content="编辑" placement="top">
                    <el-button
                      type="text"
                      icon="el-icon-edit"
                      @click.stop="editDraft(item)"
                    />
                  </el-tooltip>
                  <!-- <el-tooltip content="下载Word" placement="top">
                  <el-button
                    type="text"
                    icon="el-icon-download"
                    @click.stop="downloadWord(item)"
                  />
                </el-tooltip> -->
                  <el-tooltip content="删除" placement="top">
                    <el-button
                      type="text"
                      icon="el-icon-delete"
                      @click.stop="removeDraft(item)"
                    />
                  </el-tooltip>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-empty v-else description="暂无稿件">
        <el-button type="primary" icon="el-icon-plus" @click="handleNew"
          >去创建</el-button
        >
      </el-empty>
    </div>

    <div class="pager" v-if="total > 0">
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :page-sizes="[8, 12, 24, 48]"
        layout="prev, pager, next, jumper, ->, sizes, total"
        @pagination="handlePagination"
      />
    </div>
  </div>
</template>

<script>
import {
  listZntXz,
  delZntXz,
  exportZntXz,
  downloadZntXzWord,
} from "@/api/gongwen/mydrafts";

export default {
  name: "MyDrafts",
  data() {
    return {
      loading: false,
      list: [],
      total: 0,
      activeTab: "zntxz", // 当前激活的标签页
      queryParams: {
        pageNum: 1,
        pageSize: 12,
        keyword: "",
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleNew() {
      // 跳转到智能体写作创建页面
      this.$router.push({ path: "/gongwen/editor" });
    },
    viewDraft(item) {
      this.$router.push({
        path: `/gongwen/editor/${item.id}`,
      });
    },
    editDraft(item) {
      this.$router.push({
        path: `/gongwen/editor/${item.id}`,
      });
    },
    async removeDraft(item) {
      try {
        await this.$confirm(
          `确认删除稿件《${item.title || "未命名稿件"}》吗？`,
          "提示",
          { type: "warning" }
        );
        await delZntXz(item.id);
        this.msgSuccess("删除成功");
        this.getList();
      } catch (e) {
        // 取消或失败均忽略
      }
    },

    // 导出智能体写作列表
    async handleExport() {
      try {
        this.loading = true;
        await exportZntXz(this.queryParams);
        this.msgSuccess("导出成功");
      } catch (error) {
        this.msgError("导出失败");
      } finally {
        this.loading = false;
      }
    },
    // 下载Word文档
    async downloadWord(item) {
      try {
        this.loading = true;
        const response = await downloadZntXzWord(item.id);
        // 创建下载链接
        const blob = new Blob([response]);
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `${item.title || "智能体写作"}.docx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        this.msgSuccess("下载成功");
      } catch (error) {
        this.msgError("下载失败");
      } finally {
        this.loading = false;
      }
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handlePagination(pagination) {
      this.queryParams.pageNum = pagination.page;
      this.queryParams.pageSize = pagination.limit;
      this.getList();
    },
    async getList() {
      this.loading = true;
      try {
        // 构建智能体写作查询参数
        const zntParams = {
          pageNum: this.queryParams.pageNum,
          pageSize: this.queryParams.pageSize,
        };
        // 如果有关键字搜索，添加到title字段
        if (this.queryParams.keyword) {
          zntParams.title = this.queryParams.keyword;
        }
        const res = await listZntXz(zntParams);
        this.list = res.rows || res.data || [];
        this.total =
          res.total || (Array.isArray(this.list) ? this.list.length : 0);
      } finally {
        this.loading = false;
      }
    },
    formatDate(val) {
      if (!val) return "";
      // 支持时间戳或字符串
      const d =
        typeof val === "number"
          ? new Date(val)
          : new Date(val.replace(/-/g, "/"));
      const y = d.getFullYear();
      const m = `${d.getMonth() + 1}`.padStart(2, "0");
      const da = `${d.getDate()}`.padStart(2, "0");
      return `${y}-${m}-${da}`;
    },
    // 获取清理后的内容预览
    getCleanContentPreview(content) {
      if (!content) return "";

      // 移除 style 和 script 标签及其内容
      let cleanContent = content
        .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, "")
        .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, "")
        .replace(/\sclass\s*=\s*["'][^"']*["']/gi, "")
        // 将所有开始标签转换为 <p>
        .replace(/<[^\/][^>]*>/gi, "<p>")
        // 将所有结束标签转换为 </p>
        .replace(/<\/[^>]+>/gi, "</p>");

      // 截取前100个字符
      const preview = cleanContent.substring(0, 100);
      return preview + (cleanContent.length > 100 ? "..." : "");
    },
  },
};
</script>

<style lang="scss" scoped>
.mydrafts-page {
  padding: 12px 16px 24px;
  .topbar {
    display: grid;
    grid-template-columns: auto 1fr auto;
    grid-column-gap: 16px;
    align-items: center;
    margin-bottom: 8px;
    .top-title {
      font-size: 18px;
      font-weight: 600;
      color: #1f2d3d;
    }
    .actions {
      white-space: nowrap;
    }
  }
  .tabs-wrapper {
    margin-bottom: 16px;
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
    }
  }
  .banner {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18px 20px;
    border: 1px dashed #b3d8ff;
    border-radius: 8px;
    background: linear-gradient(90deg, #f7fbff 0%, #ffffff 60%);
    margin-bottom: 12px;
    .banner-left {
      .banner-title {
        font-size: 16px;
        font-weight: 600;
        color: #409eff;
        display: flex;
        align-items: center;
      }
      .banner-title .plus {
        margin-right: 6px;
        font-size: 18px;
      }
      .banner-desc {
        margin-top: 6px;
        color: #657180;
        font-size: 13px;
      }
      .banner-links {
        margin-top: 8px;
        .dot {
          margin: 0 8px;
          color: #c0c4cc;
        }
      }
    }
    .banner-right {
      color: #8cc5ff;
      .illus {
        font-size: 46px;
      }
    }
  }
  .list-wrapper {
    min-height: 120px;
  }
  .card-skeleton {
    height: 140px;
    border-radius: 8px;
    background: #f5f7fa;
    margin-bottom: 16px;
  }
  .draft-card {
    cursor: default;
    margin-bottom: 12px;
    height: 200px; // 固定卡片高度
    display: flex;
    flex-direction: column;

    // 让卡片内容区域也使用flex布局
    :deep(.el-card__body) {
      display: flex;
      flex-direction: column;
      height: 100%;
      padding: 16px;
    }

    .card-body {
      display: flex;
      flex-direction: column;
      height: 165px;
    }
    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #909399;
      font-size: 12px;
      flex-shrink: 0; // 防止压缩
    }
    .card-header .id {
      display: flex;
      align-items: center;
    }
    .card-header .id i {
      margin-right: 4px;
    }
    .card-title {
      margin: 6px 0 8px;
      font-weight: 600;
      line-height: 22px;
      height: 22px;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      line-clamp: 1;
      -webkit-box-orient: vertical;
      cursor: pointer;
      color: #303133;
      flex: 1; // 占据剩余空间但允许收缩
      max-width: calc(100% - 80px); // 为tag预留空间
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .card-content {
      margin: 0;
      flex: 1; // 占据剩余空间
      overflow: hidden;
    }
    .content-preview {
      font-size: 12px;
      color: #909399;
      line-height: 18px;
      // max-height: 54px; // 增加高度以适应固定卡片高度
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 3; // 增加到3行
      line-clamp: 3;
      -webkit-box-orient: vertical;
    }
    .card-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #909399;
      font-size: 12px;
      flex-shrink: 0; // 防止压缩
      margin-top: auto; // 推到底部
    }
    .ops .el-button {
      padding: 0 6px;
    }
  }
  .pager {
    text-align: center;
    margin-top: 8px;
  }
}
</style>
