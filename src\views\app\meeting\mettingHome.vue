<template>
  <div class="meeting-home-page">
    <div class="head_box">
      <img src="@/assets/images/meeting_head.png" alt="" />
    </div>
    <div class="meet_box">
      <img src="@/assets/images/meeting_left.png" alt="" @click="goMeeting" />
      <img src="@/assets/images/meeting_right.png" alt="" @click="goImport" />
    </div>
    
    <!-- 音频上传對話框 -->
    <el-dialog
      title="上传音频文件"
      :visible.sync="uploadDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="upload-container">
        <el-upload
          ref="audioUpload"
          :auto-upload="false"
          :before-upload="handleBeforeUpload"
          :on-change="handleFileChange"
          :file-list="fileList"
          :limit="1"
          :accept="acceptedAudioTypes"
          drag
          class="audio-uploader"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将音频文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">
            支持格式：{{ supportedFormats.join('、') }}，文件大小不超过 {{ maxFileSize }}MB
          </div>
        </el-upload>
        
        <!-- 上传進度 -->
        <div v-if="uploading" class="upload-progress">
          <el-progress 
            :percentage="uploadProgress" 
            :status="uploadStatus === 'success' ? 'success' : uploadStatus === 'exception' ? 'exception' : undefined"
          ></el-progress>
          <p class="progress-text">{{ uploadProgressText }}</p>
        </div>
        
        <!-- 已上传文件信息 -->
        <div v-if="uploadedFile" class="uploaded-file-info">
          <div class="file-info">
            <i class="el-icon-document"></i>
            <span class="file-name">{{ uploadedFile.name }}</span>
            <span class="file-size">({{ formatFileSize(uploadedFile.size) }})</span>
          </div>
          <div class="file-actions">
            <el-button size="mini" type="danger" @click="removeFile">刪除</el-button>
          </div>
        </div>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelUpload">取消</el-button>
        <el-button 
          type="primary" 
          @click="confirmUpload"
          :disabled="!selectedFile || uploading"
        >
          确认上传
        </el-button>
      </div>
    </el-dialog>
    
    <!-- 靜態卡片區域 -->
    <div class="static-cards-section">
      <div class="section-header">
        <h2 class="section-title">历史记录</h2>
      </div>
      <div class="cards-container">
        <div class="static-card">
          <div class="card-header">
            <div class="card-title">多人对话商务座谈会.wav</div>
          </div>
          <div class="card-tags">
            <span class="tag">包装</span>
            <span class="tag">五粮液</span>
            <span class="tag">梦之蓝</span>
            <span class="tag">剑南春</span>
            <span class="tag">洋河</span>
          </div>
          <div class="card-summary">
            <div class="summary-title">总结概要</div>
            <div class="summary-content">酒类品鉴反馈会议，讨论包装、口感、香气、品质等方面，涉及五粮液1995、梦之蓝M3、剑南春等品牌</div>
          </div>
          <div class="card-meta">
            <span class="duration">09m</span>
            <span class="date">2025/09/28 14:30</span>
          </div>
        </div>
        
        <div class="static-card">
          <div class="card-header">
            <div class="card-title">AI会议记录工具应用示例</div>
          </div>
          <div class="card-summary">
            <div class="summary-title">总结概要</div>
            <div class="summary-content">开会使用 AI 可在 5 分钟内整理好记录，具备录音、生成会议摘要、支持多语种文字、高效内容记录以及实时语音转文字等功能，还能支持多种文件格式。</div>
          </div>
          <div class="card-meta">
            <span class="duration">03s</span>
            <span class="date">2025/09/28 14:17</span>
          </div>
        </div>
        
        <div class="static-card">
          <div class="card-header">
            <div class="card-title">音频资料管理要点.wav</div>
          </div>
          <div class="card-tags">
            <span class="tag">政务场景</span>
            <span class="tag">分类</span>
           
          </div>
          <div class="card-summary">
            <div class="summary-title">总结概要</div>
            <div class="summary-content">政务场景下的音频资料，需做好分类归档，按会议主题、部门等梳理，便于后续政策复盘、工作追溯，保障政务信息管理的规范性与可查性。</div>
          </div>
          <div class="card-meta">
            <span class="duration">09m</span>
            <span class="date">2025/09/28 10:50</span>
          </div>
        </div>
        
        <div class="static-card">
          <div class="card-header">
            <div class="card-title">政务会议AI记录优势</div>
          </div>
          <div class="card-tags">
            <span class="tag">AI听记</span>
            <span class="tag">会议</span>
            <span class="tag">录音</span>
            <span class="tag">语音转文字</span>
          </div>
          <div class="card-summary">
            <div class="summary-title">总结概要</div>
            <div class="summary-content">政务开会用 AI 记录，能高效留存会议内容，实时转写确保政策传达等信息准确，还支持多格式文件，助力政务工作文档规范与信息共享。</div>
          </div>
          <div class="card-meta">
            <span class="duration">02m29s</span>
            <span class="date">2025/09/26 09:19</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";

export default {
  name: "MeetingHome",
  components: {},
  data() {
    return {
      // 上传對話框控制
      uploadDialogVisible: false,
      // 上传相關
      uploadUrl: "/transcribe-ws1/transcribe",
      uploadHeaders: {},
      fileList: [],
      uploadedFile: null,
      selectedFile: null,
      // 上传進度
      uploading: false,
      uploadProgress: 0,
      uploadStatus: 'active', // 使用有效的 status 值
      uploadProgressText: '',
      // 文件限制
      maxFileSize: 50, // MB
      supportedFormats: ['mp3', 'wav', 'm4a', 'aac', 'ogg', 'flac'],
      acceptedAudioTypes: '.mp3,.wav,.m4a,.aac,.ogg,.flac'
    };
  },
  computed: {},
  mounted() {},

  beforeDestroy() {},
  methods: {
    goMeeting() {
      this.$router.push({
        name: "MeetingRoom",
      });
    },
    goImport() {
      this.uploadDialogVisible = true;
    },
    
    
    // 上传前驗證
    handleBeforeUpload(file) {
      // 检查文件類型
      const fileExtension = file.name.split('.').pop().toLowerCase();
      if (!this.supportedFormats.includes(fileExtension)) {
        this.$message.error(`不支持的文件格式！请上传 ${this.supportedFormats.join('、')} 格式的音频文件`);
        return false;
      }
      
      // 检查文件大小
      const isLtMaxSize = file.size / 1024 / 1024 < this.maxFileSize;
      if (!isLtMaxSize) {
        this.$message.error(`文件大小不能超过 ${this.maxFileSize}MB！`);
        return false;
      }
      
      return true;
    },
    
    // 文件选择处理
    handleFileChange(file, fileList) {
      console.log('文件选择变化:', file, fileList);
      if (file && file.raw) {
        this.selectedFile = file.raw;
        this.uploadedFile = {
          name: file.name,
          size: file.size,
          file: file.raw
        };
        console.log('已选择文件:', {
          name: file.name,
          size: file.size,
          type: file.raw.type
        });
      }
    },
    
    // 上传進度
    handleUploadProgress(event, file, fileList) {
      this.uploading = true;
      this.uploadProgress = Math.round(event.percent);
      this.uploadStatus = 'active';
      this.uploadProgressText = `正在上传 ${file.name}...`;
    },
    
    
    // 上传成功
    handleUploadSuccess(response, file, fileList) {
      this.uploading = false;
      this.uploadProgress = 100;
      this.uploadStatus = 'success';
      this.uploadProgressText = '上传成功！';
      
      // 新接口的响应处理
      this.uploadedFile = {
        name: file.name,
        size: file.size,
        url: response.url || response.data?.url || response.file_url,
        response: response // 保存完整响应用於後續处理
      };
      
      this.$message.success('音频文件上传成功！');
      console.log('上传响应:', response);
    },
    
    // 上传失败
    handleUploadError(err, file, fileList) {
      this.uploading = false;
      this.uploadStatus = 'exception';
      this.uploadProgressText = '上传失败！';
      this.$message.error('音频文件上传失败，请重试！');
    },
    
    // 刪除文件
    removeFile() {
      this.uploadedFile = null;
      this.selectedFile = null;
      this.uploadProgress = 0;
      this.uploadStatus = 'active';
      this.uploadProgressText = '';
      this.uploading = false;
      this.$refs.audioUpload.clearFiles();
    },
    
    // 取消上传
    cancelUpload() {
      this.uploadDialogVisible = false;
      this.removeFile();
    },
    
    // 确认上传
    async confirmUpload() {
      console.log('开始确认上传，selectedFile:', this.selectedFile);
      if (!this.selectedFile) {
        this.$message.warning('请先选择音频文件！');
        return;
      }
      
      // 开始上传
      this.uploading = true;
      this.uploadStatus = 'active';
      this.uploadProgressText = '正在上传文件...';
      
      try {
        const formData = new FormData();
        formData.append('file', this.selectedFile);
        
        console.log('准备上传文件:', {
          fileName: this.selectedFile.name,
          fileSize: this.selectedFile.size,
          fileType: this.selectedFile.type,
          uploadUrl: this.uploadUrl
        });
        
        // 驗證 FormData 內容
        console.log('FormData 內容:');
        for (let [key, value] of formData.entries()) {
          console.log(`${key}:`, value);
        }
        
        const response = await this.uploadFile(formData);
        
        // 上传成功
        this.uploading = false;
        this.uploadProgress = 100;
        this.uploadStatus = 'success';
        this.uploadProgressText = '上传成功！';
        
        this.uploadedFile = {
          name: this.selectedFile.name,
          size: this.selectedFile.size,
          response: response
        };
        
        this.$message.success('音频文件已成功上传并处理！');
        console.log('语音转文字結果:', response);
        
        // 關閉對話框
        this.uploadDialogVisible = false;
        
        // 跳转到會議界面並傳遞转寫內容
        this.$router.push({
          name: "MeetingRoom",
          params: {
            transcriptData: response,
            fileName: this.selectedFile.name
          }
        });
        
      } catch (error) {
        // 上传失败
        this.uploading = false;
        this.uploadStatus = 'exception';
        this.uploadProgressText = '上传失败！';
        this.$message.error('音频文件上传失败，请重試！');
        console.error('上传失败:', error);
      }
    },
    
    
    // 手動上传文件
    async uploadFile(formData) {
      console.log('发送上传请求:', {
        url: this.uploadUrl,
        method: 'POST'
      });
      
      try {
        const response = await fetch(this.uploadUrl, {
          method: 'POST',
          body: formData,
          mode: 'cors'
          // 不設置 headers，讓瀏覽器自動处理 Content-Type 和邊界
        });
        
        console.log('上传响应:', response.status, response.statusText);
        console.log('响应头:', Object.fromEntries(response.headers.entries()));
        
        if (!response.ok) {
          const errorText = await response.text();
          console.error('上传失败响应:', errorText);
          throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
        }
        
        // 嘗試解析响应
        let result;
        const contentType = response.headers.get('content-type');
        console.log('响应 Content-Type:', contentType);
        
        if (contentType && contentType.includes('application/json')) {
          result = await response.json();
        } else {
          // 如果不是 JSON，嘗試解析為文本
          const textResult = await response.text();
          console.log('非 JSON 响应:', textResult);
          result = { text: textResult, raw: true };
        }
        
        console.log('上传成功响应:', result);
        return result;
        
      } catch (error) {
        console.error('上传请求失败:', error);
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
          throw new Error('网络连接失败，请检查网络连接或服务器状态');
        } else if (error.message.includes('CORS')) {
          throw new Error('跨域请求被阻止，请网络管理员');
        } else {
          throw error;
        }
      }
    },
    
    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
  },
};
</script>

<style lang="scss" scoped>
.meeting-home-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  .head_box {
    text-align: center;
    margin-top: 200px;
    img {
      width: 491px;
      height: 36px;
    }
  }
  .meet_box {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 63px;
    img {
      width: 369px;
      height: 234px;
      cursor: pointer;
    }
  }
}

// 上传相關樣式
.upload-container {
  .audio-uploader {
    .el-upload {
      border: 2px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: border-color 0.3s;
      
      &:hover {
        border-color: #409eff;
      }
    }
    
    .el-upload-dragger {
      width: 100%;
      height: 180px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background-color: #fafafa;
      
      .el-icon-upload {
        font-size: 67px;
        color: #c0c4cc;
        margin-bottom: 16px;
      }
      
      .el-upload__text {
        color: #606266;
        font-size: 14px;
        text-align: center;
        
        em {
          color: #409eff;
          font-style: normal;
        }
      }
    }
  }
  
  .upload-progress {
    margin-top: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
    
    .progress-text {
      margin-top: 10px;
      text-align: center;
      color: #606266;
      font-size: 14px;
    }
  }
  
  .uploaded-file-info {
    margin-top: 20px;
    padding: 15px;
    background-color: #f0f9ff;
    border: 1px solid #b3d8ff;
    border-radius: 4px;
    
    .file-info {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      
      .el-icon-document {
        font-size: 20px;
        color: #409eff;
        margin-right: 8px;
      }
      
      .file-name {
        font-weight: 500;
        color: #303133;
        margin-right: 8px;
      }
      
      .file-size {
        color: #909399;
        font-size: 12px;
      }
    }
    
    .file-actions {
      text-align: right;
    }
  }
}

.dialog-footer {
  text-align: right;
}

.static-cards-section {
  padding: 20px;
  background: #f5f7fa;
}

.section-header {
  margin-bottom: 20px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.cards-container {
  display: flex;
  gap: 20px;
  justify-content: space-between;
}


.static-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 0;
  
  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}


.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  flex: 1;
}

.card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
}

.tag {
  background: #f0f9ff;
  color: #409eff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  border: 1px solid #d9ecff;
}

.card-summary {
  margin-bottom: 16px;
}

.summary-title {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
}

.summary-content {
  font-size: 14px;
  color: #909399;
  line-height: 1.5;
}

.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;
}

.duration {
  font-weight: 500;
}

.date {
  color: #c0c4cc;
}

.background-image {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1;
  pointer-events: none;
  
  img {
    width: 100%;
    height: auto;
    display: block;
    object-fit: cover;
  }
}
</style>
