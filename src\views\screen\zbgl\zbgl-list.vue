<template>
  <div class="app-container">
    <el-form
      :inline="true"
      :model="dataForm"
      ref="dataForm"
      v-show="showSearch"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item prop="bm">
        <el-input
          v-model="dataForm.bm"
          placeholder="指标编码"
          clearable
        ></el-input>
      </el-form-item>
     <!-- <el-form-item prop="mch1">-->
        <!--        <el-select v-model="dataForm.mch1" filterable placeholder="请选择指标领域（专题页面）">-->
        <!--          <el-option :label="'全部（指标领域）'" :value="''"></el-option>-->
        <!--          <el-option v-for="item in this.ymzts" :label="item.dataName" :value="item.dataCode"></el-option>-->
        <!--        </el-select>-->
      <!--   <el-cascader
          v-model="dataForm.mch1"
          :show-all-levels="true"
          placeholder="所属页面"
          :options="ymzts"
          :props="{
            children: 'children',
            label: 'dataName',
            value: 'dataCode',
            checkStrictly: true,
            multiple: false,
          }"
          clearable
          filterable
        />
      </el-form-item>-->
      <el-form-item prop="mch2">
        <el-input
          v-model="dataForm.mch2"
          placeholder="指标概述"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item prop="jb">
        <el-input
          v-model="dataForm.jb"
          placeholder="sql脚本"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item prop="sjly">
        <el-select v-model="dataForm.sjly" placeholder="数据来源">
          <el-option :label="'全部'" :value="''"></el-option>
          <el-option
            v-for="item in this.sjlys"
            :label="item.dictLabel"
            :key="item.dictLabel"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="addOrUpdateHandle('', 'add')"
          v-hasPermi="['screen:zbgl:add']"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="exportApiToPdf"
          v-hasPermi="['screen:index:export']"
        >
          导出API
        </el-button>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getDataList"
      ></right-toolbar>
    </el-row>
    <el-table
      :data="dataList"
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%"
    >
      <el-table-column
        prop="bm"
        header-align="center"
        align="center"
        width="150"
        label="指标编码"
      ></el-table-column>
      <!-- <el-table-column
        prop="mch1"
        header-align="center"
        align="center"
        label="所属页面"
      ></el-table-column> -->
      <el-table-column
        prop="mch2"
        header-align="center"
        align="center"
        label="指标概述"
      ></el-table-column>
      <el-table-column
        prop="sjly"
        header-align="center"
        align="center"
        width="220"
        label="数据来源"
      ></el-table-column>
      <!-- <el-table-column
        prop="status"
        header-align="center"
        align="center"
        width="200"
        label="状态">
        <template slot-scope="scope">
                  <el-tag v-if="scope.row.status === 0" >正常</el-tag>
                  <el-tag v-else-if="scope.row.status === 1" >停止</el-tag>
                </template>
      </el-table-column> -->
      <el-table-column
        prop="dataStatus"
        header-align="center"
        align="center"
        label="数据状态"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.dataStatus == 1">在线数据</span>
          <span v-else-if="scope.row.dataStatus == 2">离线数据</span>
          <span v-else-if="scope.row.dataStatus == 3">模拟数据</span>
          <span v-else-if="scope.row.dataStatus == 4">多种数据</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="isAudit"
        header-align="center"
        align="center"
        label="审核状态"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.isAudit == 0">待审核</span>
          <span v-else-if="scope.row.isAudit == 1">审核通过</span>
          <span v-else-if="scope.row.isAudit == 9">审核不通过</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="bz"
        header-align="center"
        align="center"
        width="300"
        label="备注"
      ></el-table-column>
      <el-table-column
        header-align="center"
        align="center"
        width="200"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['screen:zbgl:edit']"
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.id, 'update')"
          >
            修改
          </el-button>
          <el-button
            v-hasPermi="['screen:zbgl:edit']"
            type="text"
            size="small"
            @click="recordListHandle(scope.row.bm)"
          >
            修改记录
          </el-button>
          <el-button type="text" size="small" @click="examine(scope.row.bm)">
            审核记录
          </el-button>
          <el-button type="text" size="small" @click="dczbapi(scope.row.id)">
            查看API
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="logListHandle(scope.row.bm)"
          >
            查看日志
          </el-button>
          <el-button type="text" size="small" @click="changeHandle(scope.row)">
            {{ scope.row.yxtz == 1 ? '停用' : '启用' }}
          </el-button>
          <el-button
            v-hasPermi="['screen:zbgl:remove']"
            type="text"
            size="small"
            @click="deleteHandle(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="pageNum"
      :limit.sync="pageSize"
      @pagination="getDataList"
    />
    <!-- 弹窗, 新增 / 修改-->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update>
    <record-list
      v-if="recordListVisible"
      ref="recordList"
      @refreshDataList="getDataList"
    ></record-list>
    <examine-list
      v-if="examineVisible"
      ref="examineList"
      @refreshDataList="getDataList"
    ></examine-list>
    <zb-api
      v-if="zbApiVisible"
      ref="zbApi"
      @refreshDataList="getDataList"
    ></zb-api>
    <log-list
      v-if="logListVisible"
      ref="logList"
      @refreshDataList="getDataList"
    ></log-list>
  </div>
</template>

<script>
import {
  findZbDataList,
  removeZb,
  zbdcwd,
  getDpztList,
  exportApiToPdf,
  changeZb,
} from '@/api/screen/zbgl'
import AddOrUpdatezb from './zbgl-addOrUpdate.vue'
import recordListzb from './zbgl-update-record-list.vue'
import logListzb from './zbgl-log-list.vue'
import examineList from './zbgl-examine-list.vue'
import zbApizb from './zbgl-api-view.vue'
import { Loading } from 'element-ui'
import { listToTree } from '@/utils/Tree'

export default {
  data() {
    return {
      dataForm: {
        bm: '',
        mch1: '',
        mch2: '',
        sjly: '',
        jb: '',
        yxtz: 0,
      },
      sjlys: [
        {
          dictLabel: '',
          dictValue: '',
        },
      ],
      ymzts: [
        {
          dataName: '',
          dataCode: '',
        },
      ],

      // 显示搜索条件
      showSearch: true,
      dataList: [],
      total: 0,
      pageNum: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      permission: [],
      permissionVisible: {
        add: false,
        update: false,
        delete: false,
      },
      addOrUpdateVisible: false,
      recordListVisible: false,
      logListVisible: false,
      zbApiVisible: false,
      examineVisible: false,
      statuList: [
        { value: '1', label: '在线数据' },
        { value: '2', label: '离线数据' },
        { value: '3', label: '模拟数据' },
        { value: '4', label: '多种数据' },
      ],
      auditList: [
        { value: '0', label: '待审核' },
        { value: '1', label: '审核通过' },
        { value: '9', label: '审核不通过' },
      ],
    }
  },
  components: {
    addOrUpdate: AddOrUpdatezb,
    recordList: recordListzb,
    examineList: examineList,
    zbApi: zbApizb,
    logList: logListzb,
  },
  mounted() {
    this.getDpztList()
  },
  created() {
    this.getDataList()
    this.getDicts('screen_dataSource').then((response) => {
      this.sjlys = response.data
    })
  },

  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      findZbDataList({
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        bm: this.dataForm.bm,
        mch1: this.dataForm.mch1,
        mch2: this.dataForm.mch2,
        sjly: this.dataForm.sjly,
        jb: this.dataForm.jb,
      }).then((response) => {
        this.dataList = response.rows
        this.total = response.total
        this.dataListLoading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getDataList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('dataForm')
      this.handleQuery()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增、修改子页面函数
    addOrUpdateHandle(id, subType) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, subType)
      })
    },
    // 历史记录子页面函数
    recordListHandle(bm) {
      this.recordListVisible = true
      this.$nextTick(() => {
        this.$refs.recordList.init(bm)
      })
    },
    //日志
    logListHandle(bm) {
      this.logListVisible = true
      this.$nextTick(() => {
        this.$refs.logList.init(bm)
      })
    },
    // 审核记录
    examine(bm) {
      this.examineVisible = true
      this.$nextTick(() => {
        this.$refs.examineList.init(bm)
      })
    },
    // 删除
    deleteHandle(id) {
      var Ids = id
        ? [id]
        : this.dataListSelections.map((item) => {
            return item.id
          })
      if (Ids == null || Ids === '') {
        this.$message.error('请选择要删除的数据')
        return
      }
      this.$confirm(`确定进行[${Ids ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          removeZb({ id: Ids.join(',') }).then((response) => {
            this.getDataList()
            this.msgSuccess('删除成功')
          })
        })
        .catch(() => {})
    },
    //启用、停用
    changeHandle(row) {
      this.$confirm(`确定进行${row.yxtz == 1 ? '停用' : '启用'}操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          changeZb({ id: row.id, yxtz: row.yxtz == 1 ? 2 : 1 }).then(
            (response) => {
              this.getDataList()
              this.msgSuccess(row.yxtz == 1 ? '停用' : '启用' + '成功')
            }
          )
        })
        .catch(() => {})
    },
    dczbapi(id) {
      this.dataListLoading = true
      var bm = ''
      var mch1 = ''
      var mch2 = ''
      if (id.length === 0) {
        bm = this.dataForm.bm
        mch1 = this.dataForm.mch1
        mch2 = this.dataForm.mch2
      }
      zbdcwd({
        page: this.pageNum,
        limit: this.pageSize,
        id: id,
        bm: bm,
        mch1: mch1,
        mch2: mch2,
      }).then((response) => {
        this.zbApiVisible = true
        this.$nextTick(() => {
          this.$refs.zbApi.init(response.data, id, bm, mch1, mch2)
        })
        this.dataListLoading = false
      })
    },
    exportApiToPdf() {
      let loadingInstance = Loading.service({
        text: '正在拼命导出中，请稍等...',
      })
      var top = 20
      var wimHeight = document.body.offsetHeight - top * 2
      var winWidth = 1400
      var left = (document.body.offsetWidth - winWidth) / 2
      exportApiToPdf({
        bm: this.dataForm.bm,
        mch1: this.dataForm.mch1,
        mch2: this.dataForm.mch2,
        sjly: this.dataForm.sjly,
        jb: this.dataForm.jb,
      }).then((response) => {
        const blob = new Blob([response])
        const fileName = '指标API文档.pdf'
        const elink = document.createElement('a') // 创建a标签
        elink.download = fileName // 为a标签添加download属性
        // a.download = fileName; //命名下载名称
        elink.style.display = 'none'
        elink.href = URL.createObjectURL(blob)
        document.body.appendChild(elink)
        elink.click() // 点击下载
        URL.revokeObjectURL(elink.href) // 释放URL 对象
        document.body.removeChild(elink) // 释放标签
        // window.open(
        //   elink.href,
        //   '指标API文档',
        //   'height=' + wimHeight + ', width=' + winWidth + ', top=' + top + ', left=' + left + ', toolbar=no, menubar=no, scrollbars=no, resizable=no, location=no, status=no'
        // )
      })
      loadingInstance.close()
    },
    // 获取页面专题
    async getDpztList() {
      await getDpztList({}).then((response) => {
        this.ymzts = listToTree('id', 'parentCode', response.data)
      })
    },
  },
}
</script>
