<template>
  <div class="meeting-api-test">
    <h2>会议API接口测试</h2>
    
    <el-card class="test-card">
      <div slot="header">
        <span>API测试结果</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="runAllTests">运行所有测试</el-button>
      </div>
      
      <div class="test-section">
        <h3>1. 获取会议列表</h3>
        <el-button @click="testGetMeetingList" :loading="loading.list">测试列表接口</el-button>
        <pre v-if="results.list">{{ JSON.stringify(results.list, null, 2) }}</pre>
      </div>
      
      <div class="test-section">
        <h3>2. 获取会议详情</h3>
        <el-input v-model="testId" placeholder="输入会议ID" style="width: 200px; margin-right: 10px;"></el-input>
        <el-button @click="testGetMeetingDetail" :loading="loading.detail">测试详情接口</el-button>
        <pre v-if="results.detail">{{ JSON.stringify(results.detail, null, 2) }}</pre>
      </div>
      
      <div class="test-section">
        <h3>3. 新增会议</h3>
        <el-button @click="testAddMeeting" :loading="loading.add">测试新增接口</el-button>
        <pre v-if="results.add">{{ JSON.stringify(results.add, null, 2) }}</pre>
      </div>
      
      <div class="test-section">
        <h3>4. 工具函数测试</h3>
        <el-button @click="testUtilFunctions">测试工具函数</el-button>
        <pre v-if="results.utils">{{ JSON.stringify(results.utils, null, 2) }}</pre>
      </div>
    </el-card>
  </div>
</template>

<script>
import { 
  getMeetingList, 
  getMeetingDetail, 
  addMeeting, 
  formatMeetingTime, 
  getMeetingStatusText, 
  calculateMeetingDuration,
  MEETING_STATUS 
} from '@/api/meeting'

export default {
  name: 'MeetingApiTest',
  data() {
    return {
      testId: '1',
      loading: {
        list: false,
        detail: false,
        add: false
      },
      results: {
        list: null,
        detail: null,
        add: null,
        utils: null
      }
    }
  },
  methods: {
    async testGetMeetingList() {
      this.loading.list = true
      try {
        const result = await getMeetingList({
          pageNum: 1,
          pageSize: 10,
          searchValue: ''
        })
        this.results.list = result
        this.$message.success('列表接口测试成功')
      } catch (error) {
        this.results.list = { error: error.message }
        this.$message.error('列表接口测试失败: ' + error.message)
      } finally {
        this.loading.list = false
      }
    },
    
    async testGetMeetingDetail() {
      if (!this.testId) {
        this.$message.warning('请输入会议ID')
        return
      }
      
      this.loading.detail = true
      try {
        const result = await getMeetingDetail(this.testId)
        this.results.detail = result
        this.$message.success('详情接口测试成功')
      } catch (error) {
        this.results.detail = { error: error.message }
        this.$message.error('详情接口测试失败: ' + error.message)
      } finally {
        this.loading.detail = false
      }
    },
    
    async testAddMeeting() {
      this.loading.add = true
      try {
        const testData = {
          title: '测试会议 - ' + new Date().toLocaleString(),
          room: 'TEST_ROOM_' + Date.now(),
          jy: '<h2>测试会议纪要</h2><p>这是一个API测试生成的会议纪要</p>',
          creator: '测试用户',
          startTime: new Date().toISOString(),
          endTime: new Date(Date.now() + 3600000).toISOString(),
          status: MEETING_STATUS.STARTED,
          details: [
            {
              nickName: '测试用户1',
              avatar: '',
              content: '这是测试发言内容1'
            },
            {
              nickName: '测试用户2', 
              avatar: '',
              content: '这是测试发言内容2'
            }
          ]
        }
        
        const result = await addMeeting(testData)
        this.results.add = result
        this.$message.success('新增接口测试成功')
      } catch (error) {
        this.results.add = { error: error.message }
        this.$message.error('新增接口测试失败: ' + error.message)
      } finally {
        this.loading.add = false
      }
    },
    
    testUtilFunctions() {
      const now = new Date()
      const later = new Date(now.getTime() + 3600000) // 1小时后
      
      this.results.utils = {
        formatMeetingTime: {
          input: now.toISOString(),
          output: formatMeetingTime(now.toISOString())
        },
        getMeetingStatusText: {
          started: getMeetingStatusText(MEETING_STATUS.STARTED),
          ended: getMeetingStatusText(MEETING_STATUS.ENDED)
        },
        calculateMeetingDuration: {
          input: { start: now.toISOString(), end: later.toISOString() },
          output: calculateMeetingDuration(now.toISOString(), later.toISOString())
        },
        constants: {
          MEETING_STATUS
        }
      }
      
      this.$message.success('工具函数测试完成')
    },
    
    async runAllTests() {
      await this.testGetMeetingList()
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      if (this.testId) {
        await this.testGetMeetingDetail()
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
      
      await this.testAddMeeting()
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      this.testUtilFunctions()
      
      this.$message.success('所有测试完成')
    }
  }
}
</script>

<style lang="scss" scoped>
.meeting-api-test {
  padding: 20px;
  
  .test-card {
    margin-bottom: 20px;
  }
  
  .test-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #ebeef5;
    
    &:last-child {
      border-bottom: none;
    }
    
    h3 {
      color: #303133;
      margin-bottom: 15px;
    }
    
    pre {
      background: #f5f7fa;
      padding: 15px;
      border-radius: 4px;
      margin-top: 15px;
      max-height: 300px;
      overflow-y: auto;
      font-size: 12px;
      line-height: 1.4;
    }
  }
}
</style>
