# 通用工作流流式接口

基于 `http://**************/v1/workflows/run` 和 API Key `app-c6FyRbJzOfsetCDbUO9zRdr7` 的通用流式调用封装。

## 功能特性

- 🚀 **流式输出**: 实时接收生成内容，提升用户体验
- 🔧 **通用接口**: 支持各种类型的工作流调用
- 🎯 **专用方法**: 提供公文生成、会议纪要等专用接口
- 🛡️ **错误处理**: 完善的错误处理和重试机制
- ⏹️ **可中断**: 支持取消正在进行的生成任务
- 📝 **思考过程**: 支持显示AI思考过程

## 快速开始

### 基本用法

```javascript
import { callWorkflowStream } from '@/api/common/workflow-stream'

const inputs = {
  prompt: '请写一篇关于AI的文章',
  length: 1000
}

const options = {
  onProgress: (chunk, fullContent) => {
    console.log('新内容:', chunk)
    // 实时更新UI
    document.getElementById('content').textContent = fullContent
  },
  onComplete: (finalContent) => {
    console.log('生成完成:', finalContent)
  },
  onError: (error) => {
    console.error('生成失败:', error)
  }
}

const result = await callWorkflowStream(inputs, options)
```

### 公文生成

```javascript
import { generateGongwenStream } from '@/api/common/workflow-stream'

const params = {
  type: '通知',
  title: '关于开展工作检查的通知',
  requirements: '内容需明确时间、范围、要求',
  outline: '一、检查时间\n二、检查范围\n三、工作要求',
  min: 500,
  max: 1000
}

await generateGongwenStream(params, {
  onProgress: (chunk, fullContent) => {
    // 实时显示生成的公文内容
  },
  onComplete: (finalContent) => {
    // 生成完成处理
  }
})
```

### 会议纪要生成

```javascript
import { generateMeetingMinutesStream } from '@/api/common/workflow-stream'

const params = {
  title: '项目讨论会议',
  time: '2024-01-15 14:00:00',
  sponsor: '张经理',
  content: [
    {
      nickName: '张经理',
      content: '今天讨论项目进度',
      time: '14:00:15'
    }
  ]
}

await generateMeetingMinutesStream(params, options)
```

## API 参考

### callWorkflowStream(inputs, options)

通用流式工作流调用接口。

**参数:**
- `inputs` (Object): 输入参数对象
- `options` (Object): 配置选项
  - `user` (string): 用户标识，默认 'api-user'
  - `onProgress` (Function): 进度回调 `(chunk, fullContent) => void`
  - `onComplete` (Function): 完成回调 `(finalContent) => void`
  - `onError` (Function): 错误回调 `(error) => void`
  - `onThought` (Function): 思考过程回调 `(thought) => void`
  - `controller` (AbortController): 用于取消请求

**返回值:**
```javascript
{
  success: boolean,
  data: {
    content: string,
    isStreaming: boolean
  },
  error?: {
    message: string,
    name: string
  }
}
```

### generateGongwenStream(params, options)

公文生成专用接口。

**参数:**
- `params.type` (string): 公文类型
- `params.title` (string): 公文标题
- `params.requirements` (string): 内容要求
- `params.outline` (string): 大纲
- `params.min` (number): 最小字数
- `params.max` (number): 最大字数

### generateMeetingMinutesStream(params, options)

会议纪要生成专用接口。

**参数:**
- `params.title` (string): 会议标题
- `params.time` (string): 会议时间
- `params.sponsor` (string): 会议发起人
- `params.content` (Array|string): 会议内容

## 在 Vue 组件中使用

```vue
<template>
  <div>
    <el-button @click="startGeneration" :loading="generating">
      开始生成
    </el-button>
    <el-button @click="stopGeneration" v-if="generating">
      停止生成
    </el-button>
    <div class="content">{{ content }}</div>
  </div>
</template>

<script>
import { callWorkflowStream } from '@/api/common/workflow-stream'

export default {
  data() {
    return {
      generating: false,
      content: '',
      controller: null
    }
  },
  methods: {
    async startGeneration() {
      this.generating = true
      this.content = ''
      this.controller = new AbortController()

      await callWorkflowStream(
        { prompt: '生成内容' },
        {
          controller: this.controller,
          onProgress: (chunk, fullContent) => {
            this.content = fullContent
          },
          onComplete: () => {
            this.generating = false
            this.$message.success('生成完成')
          },
          onError: (error) => {
            this.generating = false
            this.$message.error('生成失败')
          }
        }
      )
    },
    
    stopGeneration() {
      if (this.controller) {
        this.controller.abort()
        this.generating = false
      }
    }
  }
}
</script>
```

## 错误处理

```javascript
try {
  const result = await callWorkflowStream(inputs, {
    onError: (error) => {
      if (error.name === 'AbortError') {
        console.log('用户取消了生成')
      } else {
        console.error('生成失败:', error.message)
      }
    }
  })
  
  if (!result.success) {
    console.error('调用失败:', result.error.message)
  }
} catch (error) {
  console.error('网络错误:', error)
}
```

## 配置

可以通过修改 `WORKFLOW_CONFIG` 来调整配置：

```javascript
import { WORKFLOW_CONFIG } from '@/api/common/workflow-stream'

// 修改超时时间
WORKFLOW_CONFIG.timeout = 600000 // 10分钟
```

## 注意事项

1. **网络连接**: 确保能访问 `http://**************`
2. **API Key**: 确保 `app-c6FyRbJzOfsetCDbUO9zRdr7` 有效
3. **内存管理**: 长时间生成时注意内存使用
4. **错误重试**: 建议实现重试机制处理网络异常
5. **用户体验**: 提供取消功能和进度显示

## 更多示例

查看 `workflow-stream-examples.js` 文件获取更多使用示例。
