<template>
  <div class="app-container">
    <el-form :inline="true" :model="dataForm" ref="dataForm" v-show="showSearch" @keyup.enter.native="getDataList()">
      <!--<el-form-item prop="username">
        <el-input v-model="dataForm.username" placeholder="用户名" clearable></el-input>
      </el-form-item>-->
      <el-form-item prop="callername">
        <el-input v-model="dataForm.callername" placeholder="平台名称" clearable></el-input>
      </el-form-item>
      <el-form-item prop="indexid">
        <el-input v-model="dataForm.indexid" placeholder="指标编码" clearable></el-input>
      </el-form-item>
      <el-form-item prop="callstatus">
        <el-select v-model="dataForm.callstatus" placeholder="状态 " style="width:50%">
          <el-option :label="'全部'" :value="''"></el-option>
          <el-option :label="'正常'" :value="'0'"></el-option>
          <el-option :label="'异常'" :value="'1'"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button v-hasPermi="['screen:zbLog:list']" type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
      <!--<el-form-item>-->
        <!--<el-button v-if="hasPermission('delete')" type="primary" @click="deleteHandle()">批量删除</el-button>-->
      <!--</el-form-item>-->
    </el-form>
    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getDataList"></right-toolbar>
    </el-row>
    <el-table
      :data="dataList"
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <!--<el-table-column-->
      <!--type="selection"-->
      <!--header-align="center"-->
      <!--align="center"-->
      <!--width="40">-->
      <!--</el-table-column>-->
      //<el-table-column
       // prop="username"
       // header-align="center"
      //  align="center"
       // width="84"
      //  label="用户名">
      //</el-table-column>
      <el-table-column
        prop="callername"
        header-align="center"
        align="center"
        label="平台名称">
      </el-table-column>
      <el-table-column
        prop="indexid"
        header-align="center"
        align="center"
        width="168"
        label="指标编码">
      </el-table-column>
      <el-table-column
        prop="clientip"
        header-align="center"
        align="center"
        width="126"
        label="客户端ip">
      </el-table-column>
      <el-table-column
        prop="callstatus"
        header-align="center"
        align="center"
        width="80"
        label="返回状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.callstatus == 200" size="small" v-html="scope.row.callstatus"></el-tag>
          <el-tag v-else size="small" type="danger" v-html="scope.row.callstatus"></el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="message"
        header-align="center"
        align="center"
        label="提示信息">
      </el-table-column>
      <el-table-column
        prop="callduration"
        header-align="center"
        align="center"
        width="114"
        label="总时长（ms）">
      </el-table-column>
      <el-table-column
        prop="calldate"
        header-align="center"
        align="center"
        width="156"
        label="调用时间">
      </el-table-column>
      <el-table-column
        header-align="center"
        align="center"
        width="80"
        label="操作">
        <template slot-scope="scope">
          <el-button v-hasPermi="['screen:zbLog:query']" type="text" size="small"
                     @click="addOrUpdateHandle(scope.row.id)">详情
          </el-button>
          <!--<el-button v-if="hasPermission('delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">删除-->
          <!--</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="pageNum"
      :limit.sync="pageSize"
      @pagination="getDataList"
    />
    <!-- 弹窗, 新增 / 修改-->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
  import { findTaskList, removeDyf } from "@/api/screen/zbLog"
  import AddOrUpdateLog from './zblog-details.vue'

  export default {
    data () {
      return {
        dataForm: {
          id: '',
          callername: '',
          indexid: '',
          //username: '',
          clientip: '',
          calldate: '',
          callstatus: '',
          callduration: '',
          message: ''
        },
        dataList: [],
        // 显示搜索条件
        showSearch: true,
        total: 0,
        pageNum: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: [],
        permission: [],
        permissionVisible: {
          add: false,
          update: false,
          delete: false
        },
        addOrUpdateVisible: false
      }
    },
    components: {
      addOrUpdate: AddOrUpdateLog
    },
    created() {
      this.getDataList()
    },
    methods: {
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        findTaskList({
          'pageNum': this.pageNum,
          'pageSize': this.pageSize,
          'username': this.dataForm.username,
          'callername': this.dataForm.callername,
          'indexid': this.dataForm.indexid,
          'callstatus': this.dataForm.callstatus
        }).then(response=>{
          this.dataList = response.rows;
          this.total = response.total;
          this.dataListLoading = false;
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.getDataList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("dataForm");
        this.handleQuery();
      },
      // 多选
      selectionChangeHandle (val) {
        this.dataListSelections = val
      },
      // 详情子页面函数
      addOrUpdateHandle (id) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id)
        })
      },
      // 删除
      deleteHandle (id) {
        var Ids = id ? [id] : this.dataListSelections.map(item => {
          return item.id
        })
        if (Ids == null || Ids === '') {
          this.$message.error('请选择要删除的数据')
          return
        }
        this.$confirm(`确定进行[${Ids ? '删除' : '批量删除'}]操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          removeDyf({id: Ids.join(',')}).then(response=>{
            if (response && response.successful) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(response.message)
            }
          })
        }).catch(() => {})
      }
    }
  }
</script>
