import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

/* Layout */
import Layout from "@/layout";
import SimpleLayout from "@/layout/SimpleLayout";
import ParentView from "@/components/ParentView";

/**
 * Note: 路由配置项
 *
 * hidden: true                   // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true               // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect           // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'             // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
    noCache: true                // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'               // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'             // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false            // 如果设置为false，则不会在breadcrumb面包屑中显示
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: "/redirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/redirect/:path(.*)",
        component: (resolve) => require(["@/views/redirect"], resolve),
      },
    ],
  },
  {
    path: "/login",
    component: (resolve) => require(["@/views/login"], resolve),
    hidden: true,
  },
  {
    path: "/404",
    component: (resolve) => require(["@/views/error/404"], resolve),
    hidden: true,
  },
  {
    path: "/401",
    component: (resolve) => require(["@/views/error/401"], resolve),
    hidden: true,
  },
  {
    path: "",
    component: Layout,
    redirect: "index",
    children: [
      {
        path: "index",
        component: (resolve) => require(["@/views/index"], resolve),
        name: "首页",
        meta: { title: "首页", icon: "dashboard", noCache: true, affix: true },
      },
    ],
  },
  {
    path: "/user",
    component: Layout,
    hidden: true,
    redirect: "noredirect",
    children: [
      {
        path: "profile",
        component: (resolve) =>
          require(["@/views/system/user/profile/index"], resolve),
        name: "Profile",
        meta: { title: "个人中心", icon: "user" },
      },
    ],
  },
  {
    path: "/dict",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "type/data/:dictId(\\d+)",
        component: (resolve) => require(["@/views/system/dict/data"], resolve),
        name: "Data",
        meta: { title: "字典数据", icon: "" },
      },
    ],
  },
  {
    path: "/job",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "log",
        component: (resolve) => require(["@/views/monitor/job/log"], resolve),
        name: "JobLog",
        meta: { title: "调度日志" },
      },
    ],
  },
  {
    path: "/gen",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "edit/:tableId(\\d+)",
        component: (resolve) =>
          require(["@/views/tool/gen/editTable"], resolve),
        name: "GenEdit",
        meta: { title: "修改生成配置" },
      },
    ],
  },
  {
    path: "/gongwen",
    component: SimpleLayout,
    redirect: "/gongwen/mydrafts",
    name: "Gongwen",
    meta: { title: "公文智能体", icon: "documentation" },
    children: [
      {
        path: "mydrafts",
        component: (resolve) => require(["@/views/gongwen/mydrafts"], resolve),
        name: "MyDrafts",
        meta: { title: "公文智能体", icon: "documentation" },
      },
      {
        path: "editor",
        component: (resolve) => require(["@/views/gongwen/editor"], resolve),
        name: "GongwenEditor",
        meta: { title: "公文智能体", activeMenu: "/gongwen/mydrafts" },
        hidden: true,
      },
      {
        path: "editor/:id(\\d+)",
        component: (resolve) => require(["@/views/gongwen/editor"], resolve),
        name: "GongwenEdit",
        meta: { title: "公文智能体", activeMenu: "/gongwen/mydrafts" },
        hidden: true,
      },
      // {
      //   path: "view/:id(\\d+)",
      //   component: (resolve) => require(["@/views/gongwen/view"], resolve),
      //   name: "GongwenView",
      //   meta: { title: "查看稿件", activeMenu: "/gongwen/mydrafts" },
      //   hidden: true,
      // },
    ],
  },
  {
    path: "/meeting",
    component: SimpleLayout,
    redirect: "/meeting/mettingHome",
    name: "Meeting",
    meta: { title: "会议纪要智能体", icon: "video-camera" },
    children: [
      {
        path: "mettingHome",
        component: (resolve) =>
          require(["@/views/meeting/mettingHome"], resolve),
        name: "mettingHome",
        meta: { title: "会议纪要智能体", icon: "video-camera" },
      },
      {
        path: "index",
        component: (resolve) => require(["@/views/meeting/index"], resolve),
        name: "MeetingRoom",
        meta: { title: "会议纪要智能体", icon: "video-camera" },
        hidden: true,
      },
      {
        path: "list",
        component: (resolve) => require(["@/views/meeting/list"], resolve),
        name: "MeetingMinutesList",
        meta: { title: "会议纪要智能体", icon: "document" },
      },
      {
        path: "editor",
        component: (resolve) => require(["@/views/meeting/editor"], resolve),
        name: "MeetingEditor",
        meta: { title: "会议纪要智能体", icon: "edit" },
      },
    ],
  },
  {
    path: "/app",
    component: SimpleLayout,
    redirect: "/app/mydrafts",
    name: "App",
    meta: { title: "应用智能体", icon: "app" },
    children: [
      {
        path: "mydrafts",
        component: (resolve) =>
          require(["@/views/app/gongwen/mydrafts.vue"], resolve),
        name: "AppMyDrafts",
        meta: { title: "公文智能体", icon: "app" },
      },
      {
        path: "editor",
        component: (resolve) =>
          require(["@/views/app/gongwen/editor.vue"], resolve),
        name: "AppEditor",
        meta: { title: "公文智能体", activeMenu: "/app/editor" },
        hidden: true,
      },
      {
        path: "editor/:id(\\d+)",
        component: (resolve) =>
          require(["@/views/app/gongwen/editor.vue"], resolve),
        name: "AppEdit",
        meta: { title: "公文智能体", activeMenu: "/app/editor" },
        hidden: true,
      },
      {
        path: "meeting/list",
        component: (resolve) =>
          require(["@/views/app/meeting/list.vue"], resolve),
        name: "AppMyDrafts",
        meta: { title: "会议智能体", icon: "app" },
      },
      {
        path: "meeting/editor",
        component: (resolve) =>
          require(["@/views/app/meeting/editor.vue"], resolve),
        name: "AppEditor",
        meta: { title: "会议智能体" },
        hidden: true,
      },
      {
        path: "meeting/index",
        component: (resolve) =>
          require(["@/views/app/meeting/index.vue"], resolve),
        name: "AppEdit",
        meta: { title: "会议智能体" },
        hidden: true,
      },
      {
        path: "mettingHome",
        component: (resolve) =>
          require(["@/views/meeting/mettingHome"], resolve),
        name: "mettingHome",
        meta: { title: "会议纪要智能体", icon: "video-camera" },
      },
      {
        path: "index",
        component: (resolve) => require(["@/views/meeting/index"], resolve),
        name: "MeetingRoom",
        meta: { title: "会议纪要智能体", icon: "video-camera" },
        hidden: true,
      },
    ],
  },
];

export default new Router({
  mode: "history", // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes,
});
