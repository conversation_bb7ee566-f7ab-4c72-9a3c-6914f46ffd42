import request from "@/utils/request";

// 智能体纪要相关API

/**
 * 查询智能体纪要列表
 * @param {Object} query 查询参数
 * @param {string} query.searchValue 搜索值
 * @param {string} query.title 会议主题
 * @param {string} query.room 会议号
 * @param {string} query.creator 发起人
 * @param {string} query.startTime 开始时间
 * @param {string} query.endTime 结束时间
 * @param {number} query.status 状态，0开始,1结束
 * @param {string} query.createTime 创建时间
 * @param {number} query.pageNum 页码
 * @param {number} query.pageSize 每页数量
 * @returns {Promise} 返回分页数据
 */
export function getMeetingList(query) {
  return request({
    url: "/anlu/tZntJy/list",
    method: "get",
    params: query,
  });
}

/**
 * 导出智能体纪要列表
 * @param {Object} query 查询参数
 * @returns {Promise} 返回导出结果
 */
export function exportMeetingList(query) {
  return request({
    url: "/anlu/tZntJy/export",
    method: "get",
    params: query,
    responseType: "blob",
  });
}

/**
 * 获取智能体纪要详细信息
 * @param {number} id 纪要ID
 * @returns {Promise} 返回纪要详情
 */
export function getMeetingDetail(id) {
  return request({
    url: `/anlu/tZntJy/${id}`,
    method: "get",
  });
}

/**
 * 新增智能体纪要
 * @param {Object} data 纪要数据
 * @param {string} data.title 会议主题
 * @param {string} data.room 会议号
 * @param {string} data.jy 纪要内容
 * @param {string} data.creator 发起人
 * @param {string} data.startTime 开始时间
 * @param {string} data.endTime 结束时间
 * @param {number} data.status 状态，0开始,1结束
 * @param {Array} data.details 纪要详情列表
 * @returns {Promise} 返回操作结果
 */
export function addMeeting(data) {
  return request({
    url: "/anlu/tZntJy/add",
    method: "post",
    data,
  });
}

/**
 * 修改智能体纪要
 * @param {Object} data 纪要数据
 * @param {number} data.id 纪要ID
 * @param {string} data.title 会议主题
 * @param {string} data.room 会议号
 * @param {string} data.jy 纪要内容
 * @param {string} data.creator 发起人
 * @param {string} data.startTime 开始时间
 * @param {string} data.endTime 结束时间
 * @param {number} data.status 状态，0开始,1结束
 * @param {Array} data.details 纪要详情列表
 * @returns {Promise} 返回操作结果
 */
export function updateMeeting(data) {
  return request({
    url: "/anlu/tZntJy/edit",
    method: "post",
    data,
  });
}

/**
 * 删除智能体纪要
 * @param {Array<number>} ids 纪要ID数组
 * @returns {Promise} 返回操作结果
 */
export function deleteMeeting(ids) {
  return request({
    url: `/anlu/tZntJy/remove/${ids}`,
    method: "post",
  });
}

/**
 * 结束会议
 * @param {Object} data 会议数据
 * @param {number} data.id 纪要ID
 * @param {string} data.endTime 结束时间
 * @returns {Promise} 返回操作结果
 */
export function endMeeting(data) {
  return request({
    url: "/anlu/tZntJy/end",
    method: "post",
    data,
  });
}

// 兼容性函数 - 保持与原有代码的兼容性

/**
 * 创建新会议 (兼容性函数)
 * @param {Object} data 会议数据
 * @returns {Promise} 返回操作结果
 */
export function createMeeting(data) {
  return addMeeting(data);
}

/**
 * 获取会议纪要 (兼容性函数)
 * @param {number} meetingId 会议ID
 * @returns {Promise} 返回纪要详情
 */
export function getMeetingMinutes(meetingId) {
  return getMeetingDetail(meetingId);
}

/**
 * 导出会议纪要 (兼容性函数)
 * @param {number} meetingId 会议ID
 * @returns {Promise} 返回导出结果
 */
export function exportMeetingMinutes(meetingId) {
  return exportMeetingList({ id: meetingId });
}

// 会议状态常量
export const MEETING_STATUS = {
  STARTED: 0, // 会议开始
  ENDED: 1, // 会议结束
};

// 实用工具函数

/**
 * 格式化会议时间
 * @param {string} timeStr 时间字符串
 * @returns {string} 格式化后的时间
 */
export function formatMeetingTime(timeStr) {
  if (!timeStr) return "";
  const date = new Date(timeStr);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
}

/**
 * 获取会议状态文本
 * @param {number} status 状态码
 * @returns {string} 状态文本
 */
export function getMeetingStatusText(status) {
  switch (status) {
    case MEETING_STATUS.STARTED:
      return "进行中";
    case MEETING_STATUS.ENDED:
      return "已结束";
    default:
      return "未知状态";
  }
}

/**
 * 计算会议时长
 * @param {string} startTime 开始时间
 * @param {string} endTime 结束时间
 * @returns {string} 时长文本
 */
export function calculateMeetingDuration(startTime, endTime) {
  if (!startTime || !endTime) return "";

  const start = new Date(startTime);
  const end = new Date(endTime);
  const duration = end - start;

  if (duration <= 0) return "";

  const hours = Math.floor(duration / (1000 * 60 * 60));
  const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`;
  } else {
    return `${minutes}分钟`;
  }
}

// 数据类型定义 (用于TypeScript或文档参考)
/**
 * @typedef {Object} MeetingData
 * @property {number} id - 纪要ID
 * @property {string} title - 会议主题
 * @property {string} cTime - 创建时间
 * @property {string} room - 会议号
 * @property {string} jy - 纪要内容
 * @property {string} creator - 发起人
 * @property {string} startTime - 开始时间
 * @property {string} endTime - 结束时间
 * @property {number} status - 状态，0开始,1结束
 * @property {Array<MeetingDetail>} details - 纪要详情列表
 */

/**
 * @typedef {Object} MeetingDetail
 * @property {number} id - 详情ID
 * @property {number} jyId - 纪要ID
 * @property {string} nickName - 用户昵称
 * @property {string} avatar - 头像
 * @property {string} content - 内容
 */

/**
 * @typedef {Object} QueryParams
 * @property {string} searchValue - 搜索值
 * @property {string} title - 会议主题
 * @property {string} room - 会议号
 * @property {string} creator - 发起人
 * @property {string} startTime - 开始时间
 * @property {string} endTime - 结束时间
 * @property {number} status - 状态
 * @property {number} pageNum - 页码
 * @property {number} pageSize - 每页数量
 */
