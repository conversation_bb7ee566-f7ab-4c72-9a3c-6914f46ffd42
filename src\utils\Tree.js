/**
 *  方法名：listToTree
 *  label非必传项
 * */
export function listToTree(myId, pId, list, label) {
  function exists(list, parentId) {
    for (var i = 0; i < list.length; i++) {
      if (list[i][myId] == parentId) return true
    }
    return false
  }
  //  console.log(list)
  var nodes = []
  for (var i = 0; i < list.length; i++) {
    if (label) {
      let code = ''
      if (list[i].mlflbm !== undefined) {
        code = '(' + list[i].mlflbm + ')'
      }
      list[i].label = list[i][label] + code
    }
    if (myId != 'id') {
      list[i].id = list[i][myId]
    }
    var row = list[i]
    if (!exists(list, row[pId])) {
      nodes.push(row)
    }
  }
  var toDo = []
  for (var i = 0; i < nodes.length; i++) {
    toDo.push(nodes[i])
  }
  while (toDo.length) {
    var node = toDo.shift()
    for (var i = 0; i < list.length; i++) {
      var row = list[i]
      if (row[pId] == node[myId]) {
        if (node.children) {
          node.children.push(row)
        } else {
          node.children = [row]
        }
        toDo.push(row)
      }
    }
  }
  for (var j = 0; j < nodes.length; j++) { // 不加会报错  下面的js拼接 找不到children 会报错
    if (nodes[j].children == undefined) {
      nodes[j].children = []
    }
  }
  console.log(nodes)
  console.log(node)
  return nodes
}

export function treeTolist(list) {
  var result = []
  for (var i = 0; i < list.length; i++) {
    var test = list[i]
    var c = test.children

    result[result.length] = test
    if (c != undefined) {
      var a = treeTolist(c)
      result = result.concat(a)
      test.children = undefined
    }
  }
  return result
}
/**
 * 数组去除某个特定元素
 * */
export function remove(arr, val) {
  var index = arr.indexOf(val)
  if (index > -1) {
    arr.splice(index, 1)
  }
}
/**
 * 数组去重
 * */
export function unique(arr) {
  for (var i = 0; i < arr.length - 1; i++) {
    for (var j = i + 1; j < arr.length; j++) {
      if (arr[i] == arr[j]) {
        arr.splice(j, 1) // console.log(arr[j]);
        j--
      }
    }
  }
  return arr
}



export function deepTraversal(data) {
  const result = [];
  data.forEach(item => {
    const loop = data => {
      result.push(data);
      let child = data.children
      if (child) {
        for (let i = 0; i < child.length; i++) {
          child[i].pid = data.id
          loop(child[i])
        }
      }
    }
    loop(item);
  })
  return result;
}
