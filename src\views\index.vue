<style scoped></style>
<template>
  <div :style="'height:' + height">
    <h1 style="left: 35%;top: 30%;position: relative">欢迎使用安陆智能体</h1>
    <img v-if="userName=='adminA'" src="../assets/images/adminA.png" style="left: 19%;top: 24%;position: relative"/>
    <img v-else-if="userName=='adminB'" src="../assets/images/adminB.png" style="left: 27%;top: 32%;position: relative"/>
    <img v-else-if="userName=='adminC'" src="../assets/images/adminC.png" style="left: 18%;top: 32%;position: relative"/>
    <img v-else src="../assets/images/main-background.png" style="left: 30%;top: 35%;position: relative"/>
  </div>
</template>
<script>
export default {
  data () {
    return {
      height: document.documentElement.clientHeight - 94.5 + "px;",
    }
  },
  computed: {
    userName () {
      return this.$store.state.user.name
    },
  }
}
</script>
