<template>
  <div class="ai-assistant-content">
      <!-- 助手头像和介绍 -->
      <div class="assistant-header">
        <div class="assistant-avatar">
          <i class="el-icon-cpu"></i>
        </div>
        <div class="assistant-info">
          <h3>AI会议助手</h3>
          <p>智能分析会议内容，协助处理会议事务</p>
        </div>
      </div>

      <!-- AI对话区域 -->
      <div class="chat-container" ref="chatContainer">
        <div class="chat-messages">
          <div
            v-for="(message, index) in chatMessages"
            :key="index"
            class="message-item"
            :class="message.type"
          >
            <!-- AI消息 -->
            <div v-if="message.type === 'ai'" class="message-content">
              <div class="ai-avatar">
                <i class="el-icon-cpu"></i>
              </div>
              <div class="message-text">
                <div v-if="message.contentType === 'stream'">
                  <div class="stream-block">
                    <h4>
                      <i :class="getMessageThinkingCompleted(index) ? 'el-icon-check' : 'el-icon-loading'"></i>
                      思考过程
                    </h4>
                    <div class="thinking-text">{{ message.content.thinking }}</div>
                  </div>
                  <div class="stream-block" v-if="getMessageThinkingCompleted(index)">
                    <h4><i class="el-icon-document-checked"></i> 思考结果</h4>
                    <div class="answer-text" v-html="formatAnswerHtml(message.content.answer)"></div>
                  </div>
                </div>
                <div v-else>
                  {{ message.content }}
                </div>
                <div class="message-time-inner">{{ message.time }}</div>
              </div>
            </div>

            <!-- 用户消息 -->
            <div v-else class="message-content">
              <div class="message-text user-text">
                <div v-if="message.type === 'file'" class="file-message">
                  <i class="el-icon-document"></i>
                  <span>{{ message.fileName }}</span>
                </div>
                <div v-else>
                  {{ message.content }}
                </div>
                <div class="message-time-inner user-time">{{ message.time }}</div>
              </div>
              <div class="user-avatar">
                <i class="el-icon-user"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="input-container">
        <div class="input-box">
          <el-input
            v-model="inputMessage"
            type="textarea"
            :rows="2"
            placeholder="请输入您的问题或需求..."
            @keyup.ctrl.enter.native="handleSend"
            resize="none"
          />
          <div class="input-actions">
            <div class="send-area">
              <span class="send-tip">Ctrl+Enter 发送</span>
              <el-button
                type="primary"
                @click="handleSend"
                :disabled="!inputMessage.trim()"
                :loading="sending"
                size="small"
              >
                发送
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>

<script>
export default {
  name: 'AiAssistantDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    meetingMinutes: {
      type: Object,
      default: null
    },
    generating: {
      type: Boolean,
      default: false
    },
    conversationId: {
      type: String,
      default: ''
    },
    // 父组件可传入的初始查询（用于“总结会议”一键触发）
    initialQuery: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      chatMessages: [],
      inputMessage: '',
      sending: false,
      // 流式输出缓存
      streamingController: null,
      // <think> 解析状态
      streamState: { inThink: false, tail: '', thinkingCompleted: false },
      // 防重复触发
      hasStartedInitialStream: false,
      // 当前流式输出的消息索引
      currentStreamingIndex: -1
    }
  },
  computed: {
  },
  mounted() {
    this.initChat()
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        // 当抽屉打开时，尝试启动初始流
        this.$nextTick(() => {
          this.tryStartInitialStream()
        })
      } else {
        // 当抽屉关闭时，重置状态
        this.hasStartedInitialStream = false
        // 停止流式请求
        if (this.streamingController) {
          this.streamingController.abort()
          this.streamingController = null
        }
      }
    },
    initialQuery(newVal, oldVal) {
      // 只有在抽屉可见且有新查询时才触发
      // 并且新值不为空，且与旧值不同（避免重复触发）
      if (this.visible && newVal && newVal.trim() && newVal !== oldVal) {
        // 停止之前的流式请求
        if (this.streamingController) {
          this.streamingController.abort()
          this.streamingController = null
        }

        // 重置初始流状态，确保新的查询能正常触发
        this.hasStartedInitialStream = false
        // 延迟执行，避免与visible watch中的调用冲突
        this.$nextTick(() => {
          // 再次检查是否已经启动，避免重复触发
          if (!this.hasStartedInitialStream) {
            this.tryStartInitialStream()
          }
        })
      }
    },
  },
  beforeDestroy() {
    // 组件销毁前停止流式请求
    if (this.streamingController) {
      this.streamingController.abort()
      this.streamingController = null
    }
  },
  methods: {
    // 获取消息的思考完成状态
    getMessageThinkingCompleted(messageIndex) {
      // 如果是当前正在流式输出的消息，使用全局状态
      if (messageIndex === this.currentStreamingIndex) {
        return this.streamState.thinkingCompleted
      }
      // 否则，根据消息内容判断是否完成
      const message = this.chatMessages[messageIndex]
      if (message && message.contentType === 'stream') {
        // 如果有答案内容，说明思考已完成
        return message.content.answer && message.content.answer.trim().length > 0
      }
      return true
    },

    tryStartInitialStream() {
      if (this.hasStartedInitialStream) return
      const q = (this.initialQuery || '').trim()
      if (!q) return

      // 立即设置标志，防止重复调用
      this.hasStartedInitialStream = true

      // 将初始指令作为用户消息展示
      this.chatMessages.push({
        type: 'user',
        content: q,
        time: this.getCurrentTime()
      })

      // 开始流式
      this.sending = true
      this.streamChat(q).finally(() => {
        this.sending = false
        // 流式输出完成后，清空initialQuery，防止重新触发
        this.$emit('update:initialQuery', '')
      })
    },
    // 初始化聊天
    initChat() {
      if (this.chatMessages.length === 0) {
        this.chatMessages = [
          {
            type: 'ai',
            content: '您好！我是AI会议助手，我已经分析了您的会议内容。您可以向我询问会议相关的任何问题，或者让我帮您处理会议事务。',
            time: this.getCurrentTime(),
            contentType: 'text'
          }
        ]

      }
    },


    // 发送消息
    async handleSend() {
      if (!this.inputMessage.trim() || this.sending) return

      const userMessage = {
        type: 'user',
        content: this.inputMessage,
        time: this.getCurrentTime()
      }

      this.chatMessages.push(userMessage)
      const userInput = this.inputMessage
      this.inputMessage = ''
      this.sending = true

      // 流式调用
      try {
        await this.streamChat(userInput)
      } catch (e) {
        console.error('流式对话失败:', e)
        this.$message.error('AI服务调用失败')
      } finally {
        this.sending = false
        this.$nextTick(() => this.scrollToBottom())
      }
    },

    // 流式请求：输出思考过程与结果
    async streamChat(query) {
      // 重置流式状态
      this.streamState = { inThink: false, tail: '', thinkingCompleted: false }

      // 先插入一个AI占位消息，包含"思考过程/思考结果"两部分
      const placeholder = {
          type: 'ai',
        contentType: 'stream',
        content: { thinking: '', answer: '' },
        time: this.getCurrentTime()
      }
      this.chatMessages.push(placeholder)
      // 设置当前流式输出的消息索引
      this.currentStreamingIndex = this.chatMessages.length - 1
      this.$nextTick(() => this.scrollToBottom())

      const url = '/v1/chat-messages'
      const token = 'app-4So8okV2nKPILIMG3vDTYdQi'
      const body = {
        inputs: {},
        query,
        response_mode: 'streaming',
        conversation_id: this.conversationId || '',
        user: 'web-user'
      }

      const resp = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      })
      if (!resp.ok || !resp.body) {
        throw new Error(`HTTP ${resp.status}`)
      }

      const reader = resp.body.getReader()
      const decoder = new TextDecoder('utf-8')
      let buffer = ''

      const flushUpdate = () => {
        this.$nextTick(() => this.scrollToBottom())
      }

      while (true) {
        const { done, value } = await reader.read()
        if (done) break
        buffer += decoder.decode(value, { stream: true })

        // SSE 分包以\n\n分隔
        const parts = buffer.split('\n\n')
        buffer = parts.pop() || ''
        for (const part of parts) {
          const line = part.trim()
          if (!line) continue
          // 形如: data: {json}
          const dataPrefix = 'data:'
          const idx = line.indexOf(dataPrefix)
          if (idx !== -1) {
            const jsonStr = line.slice(idx + dataPrefix.length).trim()
            try {
              const payload = JSON.parse(jsonStr)
              // Dify 事件可能有多种：message.delta/answer.delta/thoughts.delta/...
              const event = payload.event || ''
              // 会话ID
              if (payload.conversation_id) {
                this.$emit('update:conversationId', payload.conversation_id)
              }
              // 将增量文本按 <think> 标签拆分到 思考过程 / 思考结果
              const delta = payload.answer || payload.data || ''
              if (delta) {
                this.appendStreamText(placeholder, String(delta))
                flushUpdate()
              }
              // 完成事件
              if (event === 'message_end' || event === 'completed') {
                placeholder.time = this.getCurrentTime()
                // 流式输出完成，重置当前流式输出索引
                this.currentStreamingIndex = -1
              }
            } catch (e) {
              // 忽略解析失败的片段
            }
          }
        }
      }
    },

    // 将增量文本解析为思考过程/思考结果（支持跨包标签）
    appendStreamText(targetMessage, chunk) {
      // 清理输入文本中的对象格式符
      let cleanChunk = String(chunk)
        .replace(/\[object Object\]/g, '')
        .replace(/\[object\s+\w+\]/g, '')
        .replace(/undefined/g, '')
        .replace(/null/g, '')

      let text = this.streamState.tail + cleanChunk
      this.streamState.tail = ''

      // 处理可能的半个标签结尾
      const possibleTagStarts = ['<think', '</think']
      const keepTailIfEndsWith = (s) => possibleTagStarts.find(t => s.endsWith(t))

      const commit = (str) => {
        if (!str) return
        if (this.streamState.inThink) {
          targetMessage.content.thinking += str
        } else {
          // 只有在思考过程完成后才显示思考结果
          if (this.streamState.thinkingCompleted) {
            // 直接添加原始文本，在显示时格式化
            targetMessage.content.answer += str
          }
        }
      }

      while (text.length) {
        const openIdx = text.indexOf('<think>')
        const closeIdx = text.indexOf('</think>')

        if (!this.streamState.inThink) {
          if (openIdx === -1) {
            // 无开标签，整段作为答案（但可能以半个标签结尾）
            const tailHit = keepTailIfEndsWith(text)
            if (tailHit) {
              this.streamState.tail = tailHit
              commit(text.slice(0, -tailHit.length))
            } else {
              commit(text)
            }
            break
          } else {
            // 先提交开标签前的内容到答案
            commit(text.slice(0, openIdx))
            text = text.slice(openIdx + '<think>'.length)
            this.streamState.inThink = true
            this.streamState.thinkingCompleted = false
            // 强制触发响应式更新
            this.$forceUpdate()
          }
        } else {
          // inThink 模式，寻找关标签
          if (closeIdx === -1) {
            // 没有完整关标签，全部作为思考过程（保留可能半标签尾部）
            const tailHit = keepTailIfEndsWith(text)
            if (tailHit) {
              this.streamState.tail = tailHit
              commit(text.slice(0, -tailHit.length))
            } else {
              commit(text)
            }
            break
          } else {
            // 关标签之前的内容作为思考过程
            commit(text.slice(0, closeIdx))
            text = text.slice(closeIdx + '</think>'.length)
            this.streamState.inThink = false
            this.streamState.thinkingCompleted = true
            // 强制触发响应式更新
            this.$forceUpdate()
          }
        }
      }
    },

    // 格式化思考结果为分点显示
    formatAnswerText(text) {
      if (!text) return text

      // 清理对象格式符和其他不需要的字符
      let cleanText = String(text)
        .replace(/\[object Object\]/g, '') // 移除 [object Object]
        .replace(/\[object\s+\w+\]/g, '') // 移除其他 [object Type] 格式
        .replace(/undefined/g, '') // 移除 undefined
        .replace(/null/g, '') // 移除 null
        .replace(/\s+/g, ' ') // 合并多个空格
        .trim()

      if (!cleanText) return ''

      // 处理Markdown格式
      cleanText = this.parseMarkdown(cleanText)

      return cleanText
    },

    // 解析Markdown格式
    parseMarkdown(text) {
      let result = text

      // 处理标题格式 ###标题
      result = result.replace(/###\s*(.+)/g, '\n**$1**\n')

      // 处理粗体格式 **文本**
      result = result.replace(/\*\*(.+?)\*\*/g, '**$1**')

      // 处理分隔线 ---
      result = result.replace(/^---$/gm, '\n---\n')

      // 处理列表项 - 项目
      result = result.replace(/^-\s*(.+)$/gm, '• $1')

      // 处理数字列表 1. 项目
      result = result.replace(/^(\d+)\.\s*(.+)$/gm, '$1. $2')

      // 处理子列表项 _项目
      result = result.replace(/^_\s*(.+)$/gm, '  • $1')

      // 处理多级列表
      result = result.replace(/^(\d+\.\d+)\s*(.+)$/gm, '  $1. $2')

      // 清理多余的空行
      result = result.replace(/\n{3,}/g, '\n\n')

      return result.trim()
    },

    // 将答案文本转换为HTML格式
    formatAnswerHtml(text) {
      if (!text) return ''

      let html = String(text)
        // 转义HTML特殊字符
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;')

      // 简单的格式化处理
      html = this.simpleFormatText(html)

      return html
    },

    // 简单格式化文本
    simpleFormatText(text) {
      // 处理粗体格式 **文本**
      text = text.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')

      // 先处理连续时间戳格式 (时间1-时间2)
      text = text.replace(/\((\d{2}:\d{2}:\d{2})-(\d{2}:\d{2}:\d{2})\)/g, '($1-$2)')

      // 在数字列表前添加换行（但不在行首）
      text = text.replace(/([^\\n])(\d+)\.\s*/g, '$1<br><br><strong>$2.</strong> ')

      // 在破折号前添加换行（但不在行首）
      text = text.replace(/([^\\n])-\s*/g, '$1<br>• ')

      // 在时间戳前添加换行（但不在行首，且不是连续时间戳格式）
      text = text.replace(/([^\\n])(\(\d{2}:\d{2}:\d{2}\))/g, '$1<br>$2')

      // 清理多余的换行
      text = text.replace(/<br>{3,}/g, '<br><br>')

      return text.trim()
    },

    // 生成AI回复
    generateAIResponse(userInput) {
      const input = userInput.toLowerCase()

      if (input.includes('总结') || input.includes('摘要')) {
        return '根据会议内容，我已经为您生成了详细的会议摘要，包含了关键要点、决策事项和行动项。您还需要我补充什么信息吗？'
      } else if (input.includes('导出') || input.includes('下载')) {
        return '我可以帮您将会议纪要导出为Word文档或PDF格式。请告诉我您希望导出哪种格式？'
      } else if (input.includes('行动') || input.includes('任务')) {
        return '根据会议讨论，我识别出了几个重要的行动项。您希望我为这些任务设置提醒或分配给相关人员吗？'
      } else if (input.includes('时间') || input.includes('安排')) {
        return '关于时间安排，我建议根据会议中讨论的优先级来制定时间表。您希望我帮您制定详细的时间计划吗？'
      } else {
        return '我理解您的问题。基于会议内容，我建议您可以进一步明确具体需求，这样我能为您提供更精准的帮助。'
      }
    },

    // 处理文件上传
    handleFileUpload(file) {
      const fileMessage = {
        type: 'file',
        fileName: file.name,
        content: `已上传文件：${file.name}`,
        time: this.getCurrentTime()
      }

      this.chatMessages.push(fileMessage)

      // 模拟AI处理文件
      setTimeout(() => {
        this.chatMessages.push({
          type: 'ai',
          content: `我已经收到您上传的文件"${file.name}"，正在分析文件内容。请稍等片刻，我会结合文件内容为您提供更准确的建议。`,
          time: this.getCurrentTime(),
          contentType: 'text'
        })
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }, 500)

      this.scrollToBottom()
      return false // 阻止默认上传行为
    },

    // 处理语音消息
    handleVoice() {
      this.$message.info('语音功能开发中，敬请期待')
    },

    // 重新生成纪要
    handleRegenerate() {
      this.$emit('regenerate')
    },

    // 导出纪要
    handleExport() {
      this.$emit('export')
    },

    // 滚动到底部
    scrollToBottom() {
      this.$nextTick(() => {
        const container = this.$refs.chatContainer
        if (container) {
          container.scrollTop = container.scrollHeight
        }
      })
    },

    // 获取当前时间
    getCurrentTime() {
      const now = new Date()
      return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`
    }
  }
}
</script>

<style lang="scss" scoped>
.ai-assistant-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
}

.assistant-header {
  display: flex;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #ebeef5;
  background: #fff;

  .assistant-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;

    i {
      font-size: 24px;
      color: #fff;
    }
  }

  .assistant-info {
    h3 {
      margin: 0 0 4px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }

    p {
      margin: 0;
      color: #909399;
      font-size: 13px;
    }
  }
}

.chat-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 20px;
  background: #f8f9fa;

  .chat-messages {
    padding: 20px 0;
  }
}

.message-item {
  margin-bottom: 20px;

  &.user {
    .message-content {
      justify-content: flex-end;
    }
  }

  &.ai {
    .message-content {
      justify-content: flex-start;
    }
  }

  .message-content {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    position: relative;
    margin-bottom: 20px;
  }

  .ai-avatar {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    i {
      font-size: 18px;
      color: #fff;
    }
  }

  .user-avatar {
    width: 36px;
    height: 36px;
    background: #409eff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    i {
      font-size: 18px;
      color: #fff;
    }
  }

  .message-text {
    max-width: 70%;
    background: #fff;
    padding: 14px 16px 25px 16px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    word-wrap: break-word;
    line-height: 1.5;
    position: relative;

    &.user-text {
      background: #409eff;
      color: #fff;
    }

    .message-time-inner {
      position: absolute;
      bottom: 4px;
      right: 8px;
      font-size: 11px;
      color: rgba(144, 147, 153, 0.7);
      background: rgba(255, 255, 255, 0.8);
      padding: 4px 4px;
      border-radius: 3px;
      line-height: 1;
      white-space: nowrap;
    }

    &.user-text .message-time-inner {
      color: rgba(255, 255, 255, 0.8);
      background: rgba(64, 158, 255, 0.2);
    }
  }

  .file-message {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #409eff;

    i {
      font-size: 16px;
    }
  }
}

.stream-block {
    margin-bottom: 16px;

    h4 {
      color: #303133;
      font-size: 14px;
      font-weight: 600;
      margin: 0 0 8px 0;
      display: flex;
      align-items: center;

      i {
        margin-right: 6px;
        color: #409eff;
        font-size: 14px;
      }
    }

  .thinking-text {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 12px;
    min-height: 60px;
    max-height: 200px;
    overflow-y: auto;
    font-size: 14px;
      line-height: 1.6;
    word-wrap: break-word;
    color: #909399;
  }

  .answer-text {
        color: #606266;
    line-height: 1.8;
        font-size: 14px;

    // 粗体样式
    strong {
      font-weight: 600;
      color: #303133;
    }

    // 段落样式
    p {
      margin: 8px 0;
      line-height: 1.6;
    }
  }
}

.input-container {
  border-top: 1px solid #ebeef5;
  background: #fff;
  padding: 16px 20px;

  .input-box {
    .input-actions {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 12px;

      .send-area {
        display: flex;
        align-items: center;
        gap: 12px;

        .send-tip {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
}



// 自定义输入框样式
::v-deep .el-textarea {
  .el-textarea__inner {
    border: 1px solid #dcdfe6;
    border-radius: 8px;
    padding: 12px;
    font-size: 14px;
    line-height: 1.4;
    resize: none;

    &:focus {
      border-color: #409eff;
    }
  }
}

</style>
