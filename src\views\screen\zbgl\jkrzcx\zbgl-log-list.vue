<template>
  <div class="app-container1">
    <el-dialog
      :title="dataForm.title"
      :modal-append-to-body="false"
      :close-on-click-modal="true"
      :visible.sync="visible"
      width="75%"
    >
      <el-table
        :data="dataList"
        v-loading="dataListLoading"
        style="width: 100%"
      >
        <el-table-column
          prop="dqzt"
          header-align="center"
          align="center"
          min-width="150"
          label="当前状态"
        ></el-table-column>

        <el-table-column
          prop="calldate"
          header-align="center"
          align="center"
          min-width="160"
          label="最近一次交换开始时间"
        ></el-table-column>
        <el-table-column
          prop="calldateEnd"
          header-align="center"
          align="center"
          min-width="160"
          label="最近一次交换结束时间"
        ></el-table-column>
        <el-table-column
          prop="num"
          header-align="center"
          align="center"
          min-width="160"
          label="最近一次交换数据量"
        ></el-table-column>
        <el-table-column
          prop="callstatus"
          header-align="center"
          align="center"
          min-width="160"
          label="调用状态"
        ></el-table-column>
        <el-table-column
          prop="userName"
          header-align="center"
          align="center"
          min-width="160"
          label="使用者"
        ></el-table-column>
        <el-table-column
          prop="clientip"
          header-align="center"
          align="center"
          min-width="160"
          label="使用IP"
        ></el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="pageNum"
        :limit.sync="pageSize"
        @pagination="getDataList"
      />
    </el-dialog>
  </div>
</template>

<script>
import { getApiList } from '@/api/screen/zbgl/jkrzcs.js'

export default {
  data() {
    return {
      visible: false,
      dataForm: {
        title: '',
        bm: '',
      },
      total: 0,
      dataList: [],
      pageNum: 1,
      pageSize: 10,
      dataListLoading: false,
    }
  },
  components: {},
  activated() {
    this.getDataList()
  },
  mounted() {},
  created() {
  },
  methods: {
    // 获取数据列表
    init(bm) {
      this.visible = true
      this.dataForm.bm = bm
      this.dataForm.title = bm + ' 日志'
      this.getDataList()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      getApiList({
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        indexid: this.dataForm.bm,
      }).then((response) => {
        this.dataList = response.rows
        this.total = response.total
        this.dataListLoading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getDataList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('dataForm')
      this.handleQuery()
    },
  },
}
</script>
