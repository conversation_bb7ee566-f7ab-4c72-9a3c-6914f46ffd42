<template>
  <div class="app-container">
    <el-dialog
      :title="dataForm.title"
      :modal-append-to-body="false"
      :close-on-click-modal="true"
      :visible.sync="visible"
      width="75%">
      <!-- <el-form :inline="true" :model="dataForm" ref="dataForm" v-show="showSearch" @keyup.enter.native="getDataList()">
        <el-form-item prop="mch1">
          <el-select v-model="dataForm.mch1" filterable placeholder="请选择指标领域（专题页面）">
            <el-option :label="'全部（指标领域）'" :value="''"></el-option>
            <el-option v-for="item in this.ymzts" :label="item.dataName" :value="item.dataCode"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="mch2">
          <el-input v-model="dataForm.mch2" placeholder="指标概述" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-row :gutter="10" class="mb8">
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getDataList"></right-toolbar>
      </el-row> -->
      <el-table
        :data="dataList"
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        style="width: 100%;">
        <el-table-column
          prop="bm"
          header-align="center"
          align="center"
          width="150"
          label="指标编码">
        </el-table-column>
        <el-table-column
          prop="mch1"
          header-align="center"
          align="center"
          label="指标领域">
        </el-table-column>
        <el-table-column
          prop="mch2"
          header-align="center"
          align="center"
          label="指标概述">
        </el-table-column>
        <el-table-column
          prop="sjly"
          header-align="center"
          align="center"
          width="100"
          label="数据来源">
        </el-table-column>
        <el-table-column
          prop="bz"
          header-align="center"
          align="center"
          width="250"
          label="备注">
        </el-table-column>
        <el-table-column
          prop="xgryxm"
          header-align="center"
          align="center"
          width="100"
          label="修改人员">
        </el-table-column>
        <el-table-column
          prop="xgsj"
          header-align="center"
          align="center"
          width="160"
          label="修改时间">
        </el-table-column>
        <el-table-column
          prop="jlsj"
          header-align="center"
          align="center"
          width="160"
          label="记录时间">
        </el-table-column>
        <el-table-column
          header-align="center"
          align="center"
          width="120"
          label="操作">
          <template slot-scope="scope">
            <el-button v-hasPermi="['screen:zbgl:edit']" type="text" size="small"
                       @click="recordDetailsHandle(scope.row.id,'update')">详情
            </el-button>
<!--            <el-button v-hasPermi="['screen:zbgl:remove']" type="text" size="small" @click="deleteHandle(scope.row.id)">-->
<!--              删除-->
<!--            </el-button>-->
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="pageNum"
        :limit.sync="pageSize"
        @pagination="getDataList"
      />
      <!-- 查看详情-->
      <record-details v-if="recordDetailsVisible" ref="recordDetails" @refreshDataList="getDataList"></record-details>
    </el-dialog>
  </div>
</template>

<script>
  import {findZbRecordDataList, getDpztList} from "@/api/screen/zbgl";
  import recordDetailszb from './zbgl-record-details.vue'

  export default {
    data() {
      return {
        visible: false,
        dataForm: {
          title: '',
          bm: '',
          mch1: '',
          mch2: '',
          mch3: '',
          mch4: '',
          lrms: '',
          xgms: '',
          bz: '',
          jlsj: ''
        },
        ymzts: [{
          dataName: '',
          dataCode: ''
        }],
        // 显示搜索条件
        showSearch: true,
        total: 0,
        dataList: [],
        pageNum: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: [],
        permission: [],
        permissionVisible: {
          add: false,
          update: false,
          delete: false
        },
        recordDetailsVisible: false
      }
    },
    components: {
      recordDetails: recordDetailszb
    },
    activated() {
      this.getDataList()
    },
    mounted() {

    },
    created() {
      this.getDpztList()
    },
    methods: {
      // 获取数据列表
      init(bm) {
        this.visible = true
        this.dataForm.bm = bm
        this.dataForm.title = bm + ' 修改记录'
        this.getDataList()
      },
      // 获取数据列表
      getDataList() {
        this.dataListLoading = true
        findZbRecordDataList({
          'pageNum': this.pageNum,
          'pageSize': this.pageSize,
          'bm': this.dataForm.bm,
          'mch1': this.dataForm.mch1,
          'mch2': this.dataForm.mch2,
          'mch3': this.dataForm.mch3,
          'mch4': this.dataForm.mch4,
          'lrms': this.dataForm.lrms,
          'xgms': this.dataForm.xgms,
          'bz': this.dataForm.bz
        }).then(response => {
          this.dataList = response.rows;
          this.total = response.total;
          this.dataListLoading = false
        });
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.getDataList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("dataForm");
        this.handleQuery();
      },
      // 多选
      selectionChangeHandle(val) {
        this.dataListSelections = val
      },
      recordDetailsHandle(id, subType) {
        this.recordDetailsVisible = true
        this.$nextTick(() => {
          this.$refs.recordDetails.init(id, subType)
        })
      },
      // 获取页面专题
      getDpztList() {
        getDpztList({}).then(response => {
          this.ymzts = response.data
        });
      }
    }
  }
</script>
