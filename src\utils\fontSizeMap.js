// Word 和 字号映射文件
export const fontSizeWordMap = [
  { title: "小二", value: 24 },
  { title: "三号", value: 21.3 },
  { title: "小三", value: 20 },
  { title: "四号", value: 18.7 },
  { title: "小四", value: 16 },
  { title: "五号", value: 14 },
  { title: "小五", value: 12 },
  { title: "六号", value: 10 },
];

// 生成Quill字号白名单（像素值）
export const getQuillSizeWhitelist = () => {
  return fontSizeWordMap.map((item) => `${item.value}px`);
};

// 生成Quill字号标题映射（用于显示中文名称）
export const getQuillSizeTitleMap = () => {
  const map = {};
  fontSizeWordMap.forEach((item) => {
    map[`${item.value}px`] = item.title;
  });
  return map;
};

// 根据像素值获取中文字号名称
export const getChineseFontSizeName = (pixelValue) => {
  const item = fontSizeWordMap.find((item) => `${item.value}px` === pixelValue);
  return item ? item.title : pixelValue;
};

// 根据中文字号名称获取像素值
export const getPixelValueFromChineseName = (chineseName) => {
  const item = fontSizeWordMap.find((item) => item.title === chineseName);
  return item ? `${item.value}px` : chineseName;
};
