<template>
  <div v-if="loading" class="sso-loading">
    <div class="loading-content">
      <i class="el-icon-loading"></i>
      <p>正在验证单点登录...</p>
    </div>
  </div>
</template>

<script>
import { checkTicketInUrl, validateAndLogin } from '@/utils/sso'
import { getToken } from '@/utils/auth'

export default {
  name: 'SSOHandler',
  data() {
    return {
      loading: false
    }
  },
  async mounted() {
    await this.handleSSO()
  },
  methods: {
    async handleSSO() {
      // 如果已经有token，不需要处理SSO
      if (getToken()) {
        return
      }

      const ticketInfo = checkTicketInUrl()
      if (ticketInfo) {
        this.loading = true
        try {
          await validateAndLogin(ticketInfo.ticket, ticketInfo.service)
          
          // 验证成功，触发事件通知父组件
          this.$emit('sso-success')
          
          // 获取用户信息
          await this.$store.dispatch('GetInfo')
          
          // 跳转到目标页面
          const redirect = this.$route.query.redirect || '/'
          this.$router.push(redirect)
          
        } catch (error) {
          console.error('SSO验证失败:', error)
          this.$message.error('单点登录验证失败')
          this.$emit('sso-error', error)
          
          // 验证失败，跳转到登录页
          this.$router.push('/login')
        } finally {
          this.loading = false
        }
      }
    }
  }
}
</script>

<style scoped>
.sso-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-content {
  text-align: center;
}

.loading-content i {
  font-size: 32px;
  color: #409EFF;
  margin-bottom: 16px;
}

.loading-content p {
  font-size: 16px;
  color: #666;
  margin: 0;
}
</style>
